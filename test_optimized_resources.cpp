#include "libSCR_5000_Alg.hpp"
#include <iostream>
#include <chrono>
#include <thread>

// 简单的资源管理测试
int main() {
    std::cout << "=== 测试优化后的资源管理系统 ===" << std::endl;

    // 测试版本信息
    AlgorithmVersion version;
    GetVersionInfo(&version);
    std::cout << "算法版本: " << version.version_string << std::endl;
    std::cout << "构建时间: " << version.build_timestamp << std::endl;

    // 测试初始化
    std::cout << "\n1. 测试算法库初始化..." << std::endl;
    const char* config_path = "config/config.json";
    
    auto start_time = std::chrono::high_resolution_clock::now();
    int init_result = InitializeAlgorithmLibrary(config_path);
    auto end_time = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "初始化结果: " << init_result << " (耗时: " << duration.count() << "ms)" << std::endl;

    if (init_result == 0) {
        std::cout << "✓ 算法库初始化成功" << std::endl;
        
        // 等待一段时间模拟使用
        std::cout << "\n2. 模拟使用过程..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 测试资源释放
        std::cout << "\n3. 测试资源释放..." << std::endl;
        start_time = std::chrono::high_resolution_clock::now();
        ReleaseAllResources();
        end_time = std::chrono::high_resolution_clock::now();
        
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        std::cout << "资源释放完成 (耗时: " << duration.count() << "ms)" << std::endl;
        std::cout << "✓ 资源释放成功" << std::endl;
        
    } else {
        std::cout << "✗ 算法库初始化失败，错误码: " << init_result << std::endl;
        
        // 即使初始化失败也要尝试清理
        std::cout << "\n尝试清理资源..." << std::endl;
        ReleaseAllResources();
    }

    // 测试重复初始化
    std::cout << "\n4. 测试重复初始化..." << std::endl;
    int second_init = InitializeAlgorithmLibrary(config_path);
    std::cout << "第二次初始化结果: " << second_init << std::endl;
    
    if (second_init == 0) {
        std::cout << "✓ 重复初始化处理正确" << std::endl;
        ReleaseAllResources();
    }

    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}
