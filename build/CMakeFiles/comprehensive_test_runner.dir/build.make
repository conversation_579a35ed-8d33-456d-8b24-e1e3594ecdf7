# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Test/build

# Include any dependencies generated for this target.
include CMakeFiles/comprehensive_test_runner.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/comprehensive_test_runner.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/comprehensive_test_runner.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/comprehensive_test_runner.dir/flags.make

CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o: CMakeFiles/comprehensive_test_runner.dir/flags.make
CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o: ../test/comprehensive_test_runner.cpp
CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o: CMakeFiles/comprehensive_test_runner.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o -MF CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o.d -o CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Test/test/comprehensive_test_runner.cpp

CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Test/test/comprehensive_test_runner.cpp > CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.i

CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Test/test/comprehensive_test_runner.cpp -o CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.s

# Object files for target comprehensive_test_runner
comprehensive_test_runner_OBJECTS = \
"CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o"

# External object files for target comprehensive_test_runner
comprehensive_test_runner_EXTERNAL_OBJECTS =

comprehensive_test_runner: CMakeFiles/comprehensive_test_runner.dir/test/comprehensive_test_runner.cpp.o
comprehensive_test_runner: CMakeFiles/comprehensive_test_runner.dir/build.make
comprehensive_test_runner: libSCR_5000_AI.so
comprehensive_test_runner: /usr/local/cuda/lib64/libcudart_static.a
comprehensive_test_runner: /usr/lib/x86_64-linux-gnu/librt.a
comprehensive_test_runner: /usr/lib/x86_64-linux-gnu/librt.a
comprehensive_test_runner: CMakeFiles/comprehensive_test_runner.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable comprehensive_test_runner"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/comprehensive_test_runner.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/comprehensive_test_runner.dir/build: comprehensive_test_runner
.PHONY : CMakeFiles/comprehensive_test_runner.dir/build

CMakeFiles/comprehensive_test_runner.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/comprehensive_test_runner.dir/cmake_clean.cmake
.PHONY : CMakeFiles/comprehensive_test_runner.dir/clean

CMakeFiles/comprehensive_test_runner.dir/depend:
	cd /home/<USER>/My_Project/MSHNet_TensorRT_Test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/CMakeFiles/comprehensive_test_runner.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/comprehensive_test_runner.dir/depend

