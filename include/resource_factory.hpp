#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <mutex>
#include <functional>
#include <atomic>
#include <spdlog/spdlog.h>

namespace scr5000 {

// ==================== 资源配置结构 ====================
struct ResourceConfig {
    // GPU资源配置
    size_t gpu_memory_pool_size = 512 * 1024 * 1024;  // 512MB
    size_t host_memory_pool_size = 1024 * 1024 * 1024; // 1GB
    
    // TensorRT配置
    std::string tensorrt_engine_path;
    bool enable_tensorrt_profiling = false;
    
    // FFT配置
    int fft_rows = 1024;
    int fft_cols = 2048;
    
    // 日志配置
    std::string log_level = "info";
    std::string log_file_path;
    
    // 监控配置
    bool enable_resource_monitoring = true;
    int monitoring_interval_seconds = 10;
    
    // 从JSON文件加载配置
    static ResourceConfig loadFromFile(const std::string& config_file);
    
    // 保存配置到JSON文件
    bool saveToFile(const std::string& config_file) const;
    
    // 验证配置有效性
    bool validate() const;
    
    // 获取配置摘要
    std::string getSummary() const;
};

// ==================== 资源生命周期管理器 ====================
class ResourceLifecycleManager {
private:
    std::atomic<bool> initialized_;
    std::mutex mutex_;
    ResourceConfig config_;
    
    // 资源清理回调
    std::vector<std::function<void()>> cleanup_callbacks_;
    
    // 单例实例
    static std::unique_ptr<ResourceLifecycleManager> instance_;
    static std::mutex instance_mutex_;
    
    ResourceLifecycleManager() : initialized_(false) {}
    
public:
    ~ResourceLifecycleManager() {
        cleanup();
    }
    
    // 获取单例实例
    static ResourceLifecycleManager* getInstance();
    
    // 释放单例实例
    static void releaseInstance();
    
    // 初始化所有资源
    bool initialize(const ResourceConfig& config);
    
    // 清理所有资源
    void cleanup();
    
    // 检查是否已初始化
    bool isInitialized() const { return initialized_.load(); }
    
    // 获取配置
    const ResourceConfig& getConfig() const { return config_; }
    
    // 注册清理回调
    void registerCleanupCallback(std::function<void()> callback);
    
    // 获取资源状态报告
    std::string getStatusReport() const;
    
    // 禁止拷贝和移动
    ResourceLifecycleManager(const ResourceLifecycleManager&) = delete;
    ResourceLifecycleManager& operator=(const ResourceLifecycleManager&) = delete;
    ResourceLifecycleManager(ResourceLifecycleManager&&) = delete;
    ResourceLifecycleManager& operator=(ResourceLifecycleManager&&) = delete;
};

// ==================== 资源工厂 ====================
class ResourceFactory {
public:
    // 创建GPU资源管理器（返回指针而不是unique_ptr）
    static void* createGPUResourceManager(const ResourceConfig& config);

    // 创建内存池管理器（返回指针而不是unique_ptr）
    static void* createMemoryPoolManager(const ResourceConfig& config);

    // 创建FFT优化器（返回指针而不是unique_ptr）
    static void* createFFTOptimizer(const ResourceConfig& config);

    // 创建日志系统
    static bool setupLogging(const ResourceConfig& config);

    // 创建监控系统（返回指针而不是unique_ptr）
    static void* createResourceMonitor(const ResourceConfig& config);
};

// ==================== 资源健康检查器 ====================
class ResourceHealthChecker {
private:
    std::atomic<bool> running_;
    std::thread checker_thread_;
    std::chrono::seconds check_interval_;
    
public:
    explicit ResourceHealthChecker(std::chrono::seconds interval = std::chrono::seconds(30));
    ~ResourceHealthChecker();
    
    void start();
    void stop();
    
    // 执行一次健康检查
    struct HealthReport {
        bool gpu_manager_healthy = false;
        bool memory_pool_healthy = false;
        bool tensorrt_engine_healthy = false;
        size_t gpu_memory_used = 0;
        size_t host_memory_used = 0;
        std::string error_message;
        
        bool isHealthy() const {
            return gpu_manager_healthy && memory_pool_healthy && tensorrt_engine_healthy;
        }
    };
    
    HealthReport performHealthCheck();
    
private:
    void healthCheckLoop();
};

// ==================== 全局资源管理接口 ====================
namespace ResourceManager {
    // 初始化资源管理系统
    bool initialize(const std::string& config_file);
    bool initialize(const ResourceConfig& config);
    
    // 清理资源管理系统
    void cleanup();
    
    // 检查是否已初始化
    bool isInitialized();
    
    // 获取配置
    const ResourceConfig& getConfig();
    
    // 获取状态报告
    std::string getStatusReport();
    
    // 执行健康检查
    ResourceHealthChecker::HealthReport performHealthCheck();
    
    // 启动/停止健康检查
    void startHealthCheck();
    void stopHealthCheck();
}

// ==================== 便利宏定义 ====================
#define RESOURCE_MANAGER_ENSURE_INITIALIZED() \
    do { \
        if (!scr5000::ResourceManager::isInitialized()) { \
            spdlog::error("Resource manager not initialized"); \
            throw std::runtime_error("Resource manager not initialized"); \
        } \
    } while(0)

#define RESOURCE_MANAGER_LOG_STATUS() \
    do { \
        if (scr5000::ResourceManager::isInitialized()) { \
            spdlog::info("Resource Manager Status:\n{}", \
                        scr5000::ResourceManager::getStatusReport()); \
        } \
    } while(0)

} // namespace scr5000
