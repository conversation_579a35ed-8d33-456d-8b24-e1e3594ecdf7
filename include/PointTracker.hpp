#pragma once
#include <vector>
#include <memory>
#include <array>

#include "utils.hpp"
#include "KalmanFilter3D.hpp"

std::vector<std::pair<int, int>> hungarianMatch(const Eigen::MatrixXf& cost);

class PointTracker {
public:
    PointTracker(int max_age, int reid_age, float distance_threshold, int min_hits);

    std::vector<TrackResult> update(const std::vector<Point>& detections);

    // 新增平滑轨迹插值
    // std::vector<TrackResult> interpolateTracks(float step = 1.0f) const;
    void interpolateTracks(float step, std::function<void(const std::vector<TrackResult>&)> callback) const;

    // 获取当前帧的插值分段
    std::vector<std::vector<TrackResult>> interpolateTracks_seg(float step) const;

private:
    void match(
        const std::vector<Eigen::Vector3f>& detections,
        const std::vector<Eigen::Vector3f>& predictions,
        std::vector<std::pair<int, int>>& matched,
        std::vector<int>& unmatched_dets,
        std::vector<int>& unmatched_trks);

    void reidMatch(
        const std::vector<Eigen::Vector3f>& new_detections,
        std::vector<std::pair<int, std::shared_ptr<KalmanFilter3D>>>& matched_trackers,
        std::vector<int>& unmatched_dets);

private:
    // 持久化参数
    int max_age;
    int reid_age;
    float distance_threshold;
    int min_hits;
    int frame_count;

    // 轨迹容器
    std::vector<std::shared_ptr<KalmanFilter3D>> trackers;

    // 暂停的轨迹（用于ReID），带TTL以防无限增长
    struct SuspendedTrack {
        std::shared_ptr<KalmanFilter3D> trk;
        int ttl; // 已挂起的帧数
    };
    std::vector<SuspendedTrack> suspended;

    // 限制挂起轨迹池的大小
    static constexpr size_t SUSPENDED_MAX_SIZE = 512;
};

