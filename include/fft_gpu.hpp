#pragma once

#include <vector>
#include <complex>
#include <memory>
#include <string>
#include <cufft.h>
#include "resource_manager.hpp"

struct ColumnSegment {
    std::vector<cufftComplex> data;   // 列数据
    int segment_length;               // 本段实际行数
    int segment_start;                // 全局起始行号
};

class FFTGPUOptimizer {
public:
    FFTGPUOptimizer(int rows, int cols);
    ~FFTGPUOptimizer();

    // 禁止拷贝和移动
    FFTGPUOptimizer(const FFTGPUOptimizer&) = delete;
    FFTGPUOptimizer& operator=(const FFTGPUOptimizer&) = delete;
    FFTGPUOptimizer(FFTGPUOptimizer&&) = delete;
    FFTGPUOptimizer& operator=(FFTGPUOptimizer&&) = delete;

    // 复数FFT（GPU列方向执行）
    void performColumnwiseFFT_GPU(float* d_complexData);

    // 设备内存管理（使用资源管理器）
    scr5000::CudaMemoryRAII<float>* allocateDeviceBuffer(size_t elements);
    void deallocateDeviceBuffer(scr5000::CudaMemoryRAII<float>* buffer);

    // 维度访问
    int getRows() const { return ROWS; }
    int getCols() const { return COLS; }

    // 对特定列执行一维FFT并返回对应点的FFT结果
    std::vector<std::complex<float>> performColumnwiseFFTForCenters(
        const std::vector<std::pair<int,int>>& centers,
        const float* h_complexData);

    // 对单个列数据段执行FFT
    std::complex<float> performFFTOnSegment(
        const std::vector<cufftComplex>& segment_data,
        int target_row,
        int segment_start);

    // 获取资源信息
    std::string getResourceInfo() const;

private:
    int ROWS;
    int COLS;

    // 使用新的资源管理器
    scr5000::GPUResourceManager* gpu_manager_;
    std::string fft_plan_name_;

    // 内存缓冲区管理
    std::vector<std::unique_ptr<scr5000::CudaMemoryRAII<float>>> managed_buffers_;

    // 初始化FFT计划
    bool initializeFFTPlan();

    // 清理资源
    void cleanup();
};
