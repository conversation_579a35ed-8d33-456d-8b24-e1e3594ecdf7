#pragma once

#include <memory>
#include <mutex>
#include <atomic>
#include <string>
#include <functional>
#include <vector>
#include <unordered_map>
#include <cuda_runtime.h>
#include <cufft.h>
#include <NvInfer.h>
#include <spdlog/spdlog.h>

namespace scr5000 {

// ==================== 基础资源管理器接口 ====================
class IResourceManager {
public:
    virtual ~IResourceManager() = default;
    virtual bool initialize() = 0;
    virtual void cleanup() = 0;
    virtual bool isInitialized() const = 0;
    virtual std::string getResourceInfo() const = 0;
};

// ==================== CUDA内存RAII包装器 ====================
template<typename T>
class CudaMemoryRAII {
private:
    T* ptr_;
    size_t count_;
    bool is_valid_;

public:
    explicit CudaMemoryRAII(size_t count = 0) 
        : ptr_(nullptr), count_(count), is_valid_(false) {
        if (count > 0) {
            allocate(count);
        }
    }

    ~CudaMemoryRAII() {
        deallocate();
    }

    // 禁止拷贝
    CudaMemoryRAII(const CudaMemoryRAII&) = delete;
    CudaMemoryRAII& operator=(const CudaMemoryRAII&) = delete;

    // 允许移动
    CudaMemoryRAII(CudaMemoryRAII&& other) noexcept 
        : ptr_(other.ptr_), count_(other.count_), is_valid_(other.is_valid_) {
        other.ptr_ = nullptr;
        other.count_ = 0;
        other.is_valid_ = false;
    }

    CudaMemoryRAII& operator=(CudaMemoryRAII&& other) noexcept {
        if (this != &other) {
            deallocate();
            ptr_ = other.ptr_;
            count_ = other.count_;
            is_valid_ = other.is_valid_;
            other.ptr_ = nullptr;
            other.count_ = 0;
            other.is_valid_ = false;
        }
        return *this;
    }

    bool allocate(size_t count) {
        if (is_valid_) {
            deallocate();
        }
        
        count_ = count;
        cudaError_t status = cudaMalloc(reinterpret_cast<void**>(&ptr_), count * sizeof(T));
        if (status != cudaSuccess) {
            spdlog::error("CUDA malloc failed: {} (size: {} bytes)", 
                         cudaGetErrorString(status), count * sizeof(T));
            ptr_ = nullptr;
            count_ = 0;
            is_valid_ = false;
            return false;
        }
        
        is_valid_ = true;
        spdlog::debug("CUDA memory allocated: {} bytes at {}", count * sizeof(T), static_cast<void*>(ptr_));
        return true;
    }

    void deallocate() {
        if (ptr_ && is_valid_) {
            cudaError_t status = cudaFree(ptr_);
            if (status != cudaSuccess) {
                spdlog::warn("CUDA free failed: {}", cudaGetErrorString(status));
            } else {
                spdlog::debug("CUDA memory freed: {} bytes at {}", count_ * sizeof(T), static_cast<void*>(ptr_));
            }
            ptr_ = nullptr;
            count_ = 0;
            is_valid_ = false;
        }
    }

    T* get() const { return ptr_; }
    size_t count() const { return count_; }
    size_t size_bytes() const { return count_ * sizeof(T); }
    bool is_valid() const { return is_valid_ && ptr_ != nullptr; }

    // 重新分配
    bool resize(size_t new_count) {
        return allocate(new_count);
    }
};

// ==================== CUDA流RAII包装器 ====================
class CudaStreamRAII {
private:
    cudaStream_t stream_;
    bool is_valid_;

public:
    CudaStreamRAII() : stream_(nullptr), is_valid_(false) {
        cudaError_t status = cudaStreamCreate(&stream_);
        if (status != cudaSuccess) {
            spdlog::error("CUDA stream creation failed: {}", cudaGetErrorString(status));
            stream_ = nullptr;
            is_valid_ = false;
        } else {
            is_valid_ = true;
            spdlog::debug("CUDA stream created: {}", static_cast<void*>(stream_));
        }
    }

    ~CudaStreamRAII() {
        if (stream_ && is_valid_) {
            cudaError_t status = cudaStreamDestroy(stream_);
            if (status != cudaSuccess) {
                spdlog::warn("CUDA stream destruction failed: {}", cudaGetErrorString(status));
            } else {
                spdlog::debug("CUDA stream destroyed: {}", static_cast<void*>(stream_));
            }
        }
    }

    // 禁止拷贝和移动
    CudaStreamRAII(const CudaStreamRAII&) = delete;
    CudaStreamRAII& operator=(const CudaStreamRAII&) = delete;
    CudaStreamRAII(CudaStreamRAII&&) = delete;
    CudaStreamRAII& operator=(CudaStreamRAII&&) = delete;

    cudaStream_t get() const { return stream_; }
    bool is_valid() const { return is_valid_ && stream_ != nullptr; }

    // 同步流
    bool synchronize() {
        if (!is_valid_) return false;
        cudaError_t status = cudaStreamSynchronize(stream_);
        if (status != cudaSuccess) {
            spdlog::error("CUDA stream synchronization failed: {}", cudaGetErrorString(status));
            return false;
        }
        return true;
    }
};

// ==================== cuFFT计划RAII包装器 ====================
class CuFFTPlanRAII {
private:
    cufftHandle plan_;
    bool is_valid_;
    std::string plan_info_;

public:
    CuFFTPlanRAII() : plan_(0), is_valid_(false) {}

    ~CuFFTPlanRAII() {
        destroy();
    }

    // 禁止拷贝和移动
    CuFFTPlanRAII(const CuFFTPlanRAII&) = delete;
    CuFFTPlanRAII& operator=(const CuFFTPlanRAII&) = delete;
    CuFFTPlanRAII(CuFFTPlanRAII&&) = delete;
    CuFFTPlanRAII& operator=(CuFFTPlanRAII&&) = delete;

    bool create1D(int nx, cufftType type, int batch = 1) {
        destroy();
        
        cufftResult status = cufftPlan1d(&plan_, nx, type, batch);
        if (status != CUFFT_SUCCESS) {
            spdlog::error("cuFFT 1D plan creation failed: {}", static_cast<int>(status));
            plan_ = 0;
            is_valid_ = false;
            return false;
        }
        
        is_valid_ = true;
        plan_info_ = fmt::format("1D plan: nx={}, type={}, batch={}", nx, static_cast<int>(type), batch);
        spdlog::debug("cuFFT plan created: {}", plan_info_);
        return true;
    }

    bool createMany(int rank, int* n, int* inembed, int istride, int idist,
                   int* onembed, int ostride, int odist, cufftType type, int batch) {
        destroy();
        
        cufftResult status = cufftPlanMany(&plan_, rank, n, inembed, istride, idist,
                                          onembed, ostride, odist, type, batch);
        if (status != CUFFT_SUCCESS) {
            spdlog::error("cuFFT PlanMany creation failed: {}", static_cast<int>(status));
            plan_ = 0;
            is_valid_ = false;
            return false;
        }
        
        is_valid_ = true;
        plan_info_ = fmt::format("PlanMany: rank={}, type={}, batch={}", rank, static_cast<int>(type), batch);
        spdlog::debug("cuFFT plan created: {}", plan_info_);
        return true;
    }

    void destroy() {
        if (plan_ && is_valid_) {
            cufftResult status = cufftDestroy(plan_);
            if (status != CUFFT_SUCCESS) {
                spdlog::warn("cuFFT plan destruction failed: {}", static_cast<int>(status));
            } else {
                spdlog::debug("cuFFT plan destroyed: {}", plan_info_);
            }
            plan_ = 0;
            is_valid_ = false;
            plan_info_.clear();
        }
    }

    cufftHandle get() const { return plan_; }
    bool is_valid() const { return is_valid_ && plan_ != 0; }
    const std::string& info() const { return plan_info_; }
};

// ==================== TensorRT资源RAII包装器 ====================
struct TensorRTDeleter {
    template<typename T>
    void operator()(T* obj) {
        if (obj) {
            obj->destroy();
        }
    }
};

class TensorRTEngineRAII {
private:
    std::unique_ptr<nvinfer1::IRuntime, TensorRTDeleter> runtime_;
    std::unique_ptr<nvinfer1::ICudaEngine, TensorRTDeleter> engine_;
    std::unique_ptr<nvinfer1::IExecutionContext, TensorRTDeleter> context_;
    std::string engine_path_;
    bool is_valid_;

public:
    TensorRTEngineRAII() : is_valid_(false) {}

    ~TensorRTEngineRAII() {
        cleanup();
    }

    // 禁止拷贝和移动
    TensorRTEngineRAII(const TensorRTEngineRAII&) = delete;
    TensorRTEngineRAII& operator=(const TensorRTEngineRAII&) = delete;
    TensorRTEngineRAII(TensorRTEngineRAII&&) = delete;
    TensorRTEngineRAII& operator=(TensorRTEngineRAII&&) = delete;

    bool loadEngine(const std::string& engine_path);
    void cleanup();
    
    nvinfer1::IRuntime* getRuntime() const { return runtime_.get(); }
    nvinfer1::ICudaEngine* getEngine() const { return engine_.get(); }
    nvinfer1::IExecutionContext* getContext() const { return context_.get(); }
    bool is_valid() const { return is_valid_; }
    const std::string& getEnginePath() const { return engine_path_; }
};

// ==================== GPU资源管理器 ====================
class GPUResourceManager : public IResourceManager {
private:
    std::unique_ptr<TensorRTEngineRAII> tensorrt_engine_;
    std::unique_ptr<CudaStreamRAII> main_stream_;
    std::unordered_map<std::string, std::unique_ptr<CuFFTPlanRAII>> fft_plans_;
    std::atomic<bool> initialized_;
    mutable std::mutex mutex_;

    // GPU内存缓冲区
    CudaMemoryRAII<float> input_buffer_;
    CudaMemoryRAII<float> output_buffer_;

public:
    GPUResourceManager();
    ~GPUResourceManager();

    bool initialize() override;
    void cleanup() override;
    bool isInitialized() const override;
    std::string getResourceInfo() const override;

    // 专用接口
    bool loadTensorRTEngine(const std::string& engine_path);
    TensorRTEngineRAII* getTensorRTEngine();
    CudaStreamRAII* getMainStream();
    bool allocateBuffers(size_t input_size, size_t output_size);
    CudaMemoryRAII<float>* getInputBuffer();
    CudaMemoryRAII<float>* getOutputBuffer();
    CuFFTPlanRAII* getOrCreateFFTPlan(const std::string& plan_name,
                                     std::function<bool(CuFFTPlanRAII*)> creator);
};

// ==================== 全局访问函数 ====================
GPUResourceManager* getGPUResourceManager();
void releaseGPUResourceManager();

} // namespace scr5000
