cmake_minimum_required(VERSION 3.10)
project(SCR_5000_AI VERSION 1.4.1)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CUDA_STANDARD 17)

set(CMAKE_CUDA_ARCHITECTURES "native")

string(TIMESTAMP BUILD_TIMESTAMP "%Y-%m-%d %H:%M:%S")

# 生成配置文件
configure_file(
    ${PROJECT_SOURCE_DIR}/include/TutorialConfig.h.in
    ${PROJECT_SOURCE_DIR}/include/TutorialConfig.h
)

# 查找 OpenCV
find_package(OpenCV REQUIRED)
message(STATUS "OpenCV include: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV libs: ${OpenCV_LIBS}")

# 查找 CUDA
find_package(CUDA REQUIRED)
message(STATUS "CUDA include: ${CUDA_INCLUDE_DIRS}")
message(STATUS "CUDA libs: ${CUDA_LIBRARIES}")

find_package(OpenMP REQUIRED)


# # 查找 FFTW
# find_path(FFTW_INCLUDE_DIR fftw3.h
#     PATHS /usr/local/include
#     REQUIRED)

# # 强制使用系统的FFTW共享库
# set(FFTW_LIB "/usr/lib/x86_64-linux-gnu/libfftw3f.so")

# 手动配置 TensorRT 路径
set(TENSORRT_ROOT "$ENV{TENSORRT_ROOT}" CACHE PATH "TensorRT 安装路径")
message(STATUS "TensorRT root: ${TENSORRT_ROOT}")

# 查找 TensorRT 头文件和库

find_path(TENSORRT_BUFFERS_DIR buffers.h
    PATHS ${TENSORRT_ROOT}/samples/common
    REQUIRED)
    
find_path(TENSORRT_COMMON_DIR common.h
    PATHS ${TENSORRT_ROOT}/samples/common
    REQUIRED)

find_path(TENSORRT_INCLUDE_DIR NvInfer.h
    PATHS ${TENSORRT_ROOT}/include
    REQUIRED
)
find_library(TENSORRT_LIB nvinfer
    PATHS ${TENSORRT_ROOT}/lib
    REQUIRED
)
find_library(NVINFER_PLUGIN_LIB nvinfer_plugin
    PATHS ${TENSORRT_ROOT}/lib
    REQUIRED
)

find_library(CUFFT_LIBRARIES cufft HINTS /usr/local/cuda/lib64 REQUIRED)
message(STATUS "CUFFT libs: ${CUFFT_LIBRARIES}")

message(STATUS "TensorRT include: ${TENSORRT_INCLUDE_DIR}")
message(STATUS "TensorRT lib: ${TENSORRT_LIB}")
message(STATUS "NVIDIA Plugin lib: ${NVINFER_PLUGIN_LIB}")

# 收集源文件（排除main.cpp用于动态库）
file(GLOB_RECURSE LIB_SOURCES "src/*.cpp" "src/*.cu")
list(REMOVE_ITEM LIB_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/src/main.cpp")
file(GLOB_RECURSE HEADERS "include/*.h" "include/*.hpp")

# 确保包含新的资源管理文件
list(APPEND LIB_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/src/resource_manager.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/memory_pool.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/resource_factory.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/debug_tools.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/exception_safety.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/performance_optimizer.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/comprehensive_test.cpp"
)
list(APPEND HEADERS
    "${CMAKE_CURRENT_SOURCE_DIR}/include/resource_manager.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/memory_pool.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/resource_factory.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/debug_tools.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/exception_safety.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/performance_optimizer.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/comprehensive_test.hpp"
)

# 创建动态库
add_library(SCR_5000_AI SHARED
    ${LIB_SOURCES}
    ${HEADERS}
)

# 设置动态库的包含目录
target_include_directories(SCR_5000_AI PRIVATE
    ${PROJECT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
    ${CUDA_INCLUDE_DIRS}
    ${TENSORRT_BUFFERS_DIR}
    ${TENSORRT_COMMON_DIR}
    ${TENSORRT_INCLUDE_DIR}
    ${FFTW_INCLUDE_DIR}
)

# 链接动态库的依赖
target_link_libraries(SCR_5000_AI PRIVATE
    ${OpenCV_LIBS}
    ${CUDA_LIBRARIES}
    ${TENSORRT_LIB}
    ${TENSORRT_PLUGIN_LIB}
    ${NVINFER_PLUGIN_LIB}
    ${FFTW_LIB}
    ${CUFFT_LIBRARIES}
    OpenMP::OpenMP_CXX
)

# 设置动态库输出名称
# set_target_properties(SCR_5000_AI PROPERTIES
#     OUTPUT_NAME "SCR_5000_AI"
#     VERSION 1.0.0
#     SOVERSION 1
# )

# 添加原始可执行文件（包含main.cpp）
# add_executable(${PROJECT_NAME}
#     src/main.cpp
#     ${LIB_SOURCES}
#     ${HEADERS}
# )

# 设置可执行文件的包含目录
# target_include_directories(${PROJECT_NAME} PRIVATE
#     ${PROJECT_SOURCE_DIR}/include
#     ${OpenCV_INCLUDE_DIRS}
#     ${CUDA_INCLUDE_DIRS}
#     ${TENSORRT_BUFFERS_DIR}
#     ${TENSORRT_COMMON_DIR}
#     ${TENSORRT_INCLUDE_DIR}
#     ${FFTW_INCLUDE_DIR}
# )

# # 链接可执行文件的依赖
# target_link_libraries(${PROJECT_NAME} PRIVATE
#     ${OpenCV_LIBS}
#     ${CUDA_LIBRARIES}
#     ${TENSORRT_LIB}
#     ${TENSORRT_PLUGIN_LIB}
#     ${NVINFER_PLUGIN_LIB}
#     ${FFTW_LIB}
# )

# 创建测试程序
add_executable(test_algorithm
    test/test_algorithm.cpp
)

# 设置测试程序的包含目录
target_include_directories(test_algorithm PRIVATE
    ${PROJECT_SOURCE_DIR}/include
)

# 链接测试程序到动态库
target_link_libraries(test_algorithm PRIVATE
    SCR_5000_AI
    dl  # 用于动态加载
)

# 创建综合测试程序
add_executable(comprehensive_test_runner
    test/comprehensive_test_runner.cpp
)

# 设置综合测试程序的包含目录
target_include_directories(comprehensive_test_runner PRIVATE
    ${PROJECT_SOURCE_DIR}/include
    ${CUDA_INCLUDE_DIRS}
    ${TENSORRT_INCLUDE_DIR}
)

# 链接综合测试程序到动态库
target_link_libraries(comprehensive_test_runner PRIVATE
    SCR_5000_AI
    ${CUDA_LIBRARIES}
    dl  # 用于动态加载
)

# # 创建新预处理函数测试程序
# add_executable(test_new_preprocess
#     test/test_new_preprocess.cpp
# )

# # 设置新测试程序的包含目录
# target_include_directories(test_new_preprocess PRIVATE
#     ${PROJECT_SOURCE_DIR}/include
#     ${CUDA_INCLUDE_DIRS}
# )

# # 链接新测试程序到动态库
# target_link_libraries(test_new_preprocess PRIVATE
#     SCR_5000_AI
#     ${CUDA_LIBRARIES}
#     dl  # 用于动态加载
# )

set(CMAKE_BUILD_TYPE Debug)