#include "comprehensive_test.hpp"
#include "libSCR_5000_Alg.hpp"
#include "resource_factory.hpp"
#include "debug_tools.hpp"
#include "exception_safety.hpp"
#include "performance_optimizer.hpp"
#include <iostream>
#include <csignal>

// 全局标志用于优雅关闭
std::atomic<bool> g_shutdown_requested{false};

void signalHandler(int signal) {
    spdlog::warn("Received signal {}, initiating shutdown...", signal);
    g_shutdown_requested.store(true);
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    // 设置日志级别
    spdlog::set_level(spdlog::level::info);
    spdlog::info("=== SCR-5000 Comprehensive Test Runner ===");
    
    try {
        // 设置全局异常处理器
        scr5000::setupGlobalExceptionHandler();
        
        // 初始化资源管理系统
        spdlog::info("Initializing resource management system...");
        scr5000::ResourceConfig config;
        config.gpu_memory_pool_size = 256 * 1024 * 1024;  // 256MB
        config.host_memory_pool_size = 512 * 1024 * 1024; // 512MB
        config.log_level = "info";
        config.enable_resource_monitoring = true;
        config.monitoring_interval_seconds = 5;
        
        if (!scr5000::ResourceManager::initialize(config)) {
            spdlog::error("Failed to initialize resource management system");
            return -1;
        }
        
        // 初始化性能优化系统
        spdlog::info("Initializing performance optimization system...");
        if (!scr5000::initializePerformanceOptimization()) {
            spdlog::error("Failed to initialize performance optimization system");
            return -1;
        }
        
        // 启动调试工具
        spdlog::info("Starting debug tools...");
        auto* debug_manager = scr5000::DebugToolsManager::getInstance();
        debug_manager->enableAllMonitoring();
        
        // 启动健康检查
        scr5000::ResourceManager::startHealthCheck();
        
        // 运行综合测试
        spdlog::info("Starting comprehensive test suite...");
        
        std::string output_dir = "test_results";
        if (argc > 1) {
            output_dir = argv[1];
        }
        
        int test_result = scr5000::runComprehensiveTests(output_dir);
        
        // 等待一段时间以观察系统行为
        spdlog::info("Test completed, monitoring system for 30 seconds...");
        for (int i = 0; i < 30 && !g_shutdown_requested.load(); ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            
            // 每10秒记录一次状态
            if ((i + 1) % 10 == 0) {
                spdlog::info("System monitoring: {}s elapsed", i + 1);
                debug_manager->logSystemStatus();
                scr5000::logPerformanceStatistics();
                
                // 执行健康检查
                auto health_report = scr5000::ResourceManager::performHealthCheck();
                if (health_report.isHealthy()) {
                    spdlog::info("System health check: PASSED");
                } else {
                    spdlog::warn("System health check: FAILED - {}", health_report.error_message);
                }
            }
        }
        
        // 运行系统诊断
        spdlog::info("Running final system diagnostics...");
        debug_manager->runDiagnostics();
        
        // 导出调试数据
        spdlog::info("Exporting debug data...");
        debug_manager->exportAllData("final_debug_data");
        
        // 内存整理
        spdlog::info("Performing memory defragmentation...");
        scr5000::defragmentMemoryPools();
        
        // 最终状态报告
        spdlog::info("=== Final System Status ===");
        spdlog::info("Resource Manager Status:\n{}", scr5000::ResourceManager::getStatusReport());
        spdlog::info("Debug Tools Status:\n{}", debug_manager->getStatusSummary());
        
        // 清理资源
        spdlog::info("Cleaning up resources...");
        scr5000::ResourceManager::stopHealthCheck();
        debug_manager->disableAllMonitoring();
        scr5000::cleanupPerformanceOptimization();
        scr5000::ResourceManager::cleanup();
        scr5000::DebugToolsManager::releaseInstance();
        
        spdlog::info("Comprehensive test runner completed successfully");
        return test_result;
        
    } catch (const scr5000::SCRException& e) {
        spdlog::critical("SCR Exception: {}", e.getDetailedMessage());
        
        // 尝试清理
        try {
            scr5000::ResourceManager::cleanup();
            scr5000::cleanupPerformanceOptimization();
        } catch (...) {
            spdlog::error("Exception during emergency cleanup");
        }
        
        return -1;
        
    } catch (const std::exception& e) {
        spdlog::critical("Standard Exception: {}", e.what());
        
        // 尝试清理
        try {
            scr5000::ResourceManager::cleanup();
            scr5000::cleanupPerformanceOptimization();
        } catch (...) {
            spdlog::error("Exception during emergency cleanup");
        }
        
        return -1;
        
    } catch (...) {
        spdlog::critical("Unknown exception occurred");
        
        // 尝试清理
        try {
            scr5000::ResourceManager::cleanup();
            scr5000::cleanupPerformanceOptimization();
        } catch (...) {
            // 忽略清理时的异常
        }
        
        return -1;
    }
}

// 额外的测试函数
namespace scr5000 {

// 内存泄漏测试
TestResult testMemoryLeaks() {
    try {
        spdlog::info("Starting memory leak test...");
        
        auto* memory_monitor = DebugToolsManager::getInstance()->getMemoryMonitor();
        memory_monitor->takeSnapshot("leak_test_start");
        
        // 执行多次分配和释放
        const int iterations = 1000;
        for (int i = 0; i < iterations; ++i) {
            // CUDA内存测试
            float* cuda_ptr = allocateOptimizedCudaFloat(1024);
            if (cuda_ptr) {
                deallocateOptimizedCudaFloat(cuda_ptr);
            }
            
            // 主机内存测试
            int* host_ptr = allocateOptimizedHostInt(512);
            if (host_ptr) {
                deallocateOptimizedHostInt(host_ptr);
            }
            
            if (i % 100 == 0) {
                memory_monitor->takeSnapshot(fmt::format("leak_test_iter_{}", i));
            }
        }
        
        memory_monitor->takeSnapshot("leak_test_end");
        memory_monitor->logMemoryUsage("Memory Leak Test Completed");
        
        // 检查内存趋势
        std::string trend = memory_monitor->getMemoryTrend();
        spdlog::info("Memory trend analysis: {}", trend);
        
        // 如果趋势显示内存稳定或下降，认为测试通过
        if (trend.find("Stable") != std::string::npos || 
            trend.find("Decreasing") != std::string::npos) {
            spdlog::info("Memory leak test PASSED");
            return TestResult::PASSED;
        } else {
            spdlog::warn("Memory leak test shows increasing trend");
            return TestResult::FAILED;
        }
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in memory leak test: {}", e.what());
        return TestResult::ERROR;
    }
}

// 长期运行测试
TestResult testLongTermStability() {
    try {
        spdlog::info("Starting long-term stability test (60 seconds)...");
        
        auto start_time = std::chrono::steady_clock::now();
        auto end_time = start_time + std::chrono::seconds(60);
        
        int operation_count = 0;
        int error_count = 0;
        
        while (std::chrono::steady_clock::now() < end_time && !g_shutdown_requested.load()) {
            try {
                // 模拟典型操作
                float* cuda_data = allocateOptimizedCudaFloat(2048);
                if (cuda_data) {
                    // 模拟数据处理
                    cudaMemset(cuda_data, 0, 2048 * sizeof(float));
                    deallocateOptimizedCudaFloat(cuda_data);
                    operation_count++;
                } else {
                    error_count++;
                }
                
                // 短暂休眠
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                
            } catch (const std::exception& e) {
                spdlog::warn("Error in long-term test operation: {}", e.what());
                error_count++;
            }
        }
        
        double error_rate = static_cast<double>(error_count) / (operation_count + error_count) * 100.0;
        spdlog::info("Long-term stability test completed: {} operations, {} errors ({:.2f}% error rate)",
                    operation_count, error_count, error_rate);
        
        // 如果错误率低于5%，认为测试通过
        if (error_rate < 5.0) {
            spdlog::info("Long-term stability test PASSED");
            return TestResult::PASSED;
        } else {
            spdlog::error("Long-term stability test FAILED: high error rate");
            return TestResult::FAILED;
        }
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in long-term stability test: {}", e.what());
        return TestResult::ERROR;
    }
}

// 创建扩展测试套件
std::unique_ptr<TestSuite> createExtendedTestSuite() {
    auto suite = std::make_unique<TestSuite>("Extended Validation Tests");
    
    suite->addTestCase("Memory Leak Test", 
                      "Test for memory leaks in allocation/deallocation cycles",
                      testMemoryLeaks,
                      std::chrono::milliseconds(120000)); // 2分钟超时
    
    suite->addTestCase("Long-term Stability Test", 
                      "Test system stability under continuous operation",
                      testLongTermStability,
                      std::chrono::milliseconds(90000)); // 90秒超时
    
    return suite;
}

} // namespace scr5000
