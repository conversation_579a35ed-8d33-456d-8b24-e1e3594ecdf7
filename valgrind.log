==3059068== Memcheck, a memory error detector
==3059068== Copyright (C) 2002-2017, and GNU GPL'd, by <PERSON> et al.
==3059068== Using Valgrind-3.18.1 and LibVEX; rerun with -h for copyright info
==3059068== Command: ./build/test_algorithm
==3059068== 
==3059068== Warning: set address range perms: large range [0x15f26000, 0x27001000) (noaccess)
==3059068== Warning: set address range perms: large range [0x16000000, 0x26edb000) (defined)
==3059068== Warning: set address range perms: large range [0x2d85e000, 0x51fe4000) (noaccess)
==3059068== Warning: set address range perms: large range [0x2da00000, 0x51f86000) (defined)
[2025-08-22 14:16:00.551] [info] === SCR_5000 Enhanced Async Demo ===
[2025-08-22 14:16:00.598] [info] Initializing algorithm library...
[2025-08-22 14:16:00.600] [info] =====算法库初始化开始...=====
[2025-08-22 14:16:00.610] [info] Initializing unified resource manager...
Loaded config from config/custom_config.json
[2025-08-22 14:16:00.752] [info] Initializing GPU resource manager...
==3059068== Warning: noted but unhandled ioctl 0x30000001 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x4b with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x27 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x25 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x17 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: set address range perms: large range [0x200000000, 0x300200000) (noaccess)
==3059068== Warning: set address range perms: large range [0x5a487000, 0x7a486000) (noaccess)
==3059068== Warning: noted but unhandled ioctl 0x19 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x49 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x21 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x1b with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
==3059068== Warning: noted but unhandled ioctl 0x44 with no size/direction hints.
==3059068==    This could cause spurious value errors to appear.
==3059068==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
[2025-08-22 14:16:07.754] [info] GPU resource manager initialized successfully
[2025-08-22 14:16:07.760] [info] Initializing memory pool manager...
[2025-08-22 14:16:07.769] [info] CUDA memory pool created with max size: 512MB
[2025-08-22 14:16:07.773] [info] Host memory pool created with max size: 1024MB
[2025-08-22 14:16:07.776] [info] Memory pool manager initialized successfully
[2025-08-22 14:16:07.808] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[2025-08-22 14:16:08.006] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[2025-08-22 14:16:08.012] [info] Unified resource manager initialized successfully
Deserialization required 543619 microseconds.
Total per-runner device persistent memory is 0
Total per-runner host persistent memory is 106096
Allocated activation device memory of size 36175872
CUDA lazy loading is enabled.
[2025-08-22 14:16:11] [warning] CPU Memory at Algorithm library initialization complete: RSS 1004.88 MB
[2025-08-22 14:16:15] [warning] CPU Memory at Target detection start: RSS 1146.69 MB
[2025-08-22 14:16:30] [warning] 无效帧:33238 模型没有检测到目标，跳过处理
[2025-08-22 14:16:32] [warning] CPU Memory at Before TargetTracking: RSS 2346.52 MB
[2025-08-22 14:16:32] [warning] 没有检测到目标，跳过跟踪
[2025-08-22 14:16:33] [warning] CPU Memory at Target detection start: RSS 2341.86 MB
[2025-08-22 14:16:38] [warning] CPU Memory at Target detection complete: RSS 2503.57 MB
[2025-08-22 14:16:41] [warning] CPU Memory at Before TargetTracking: RSS 2319.15 MB
[2025-08-22 14:16:42] [warning] CPU Memory at Target detection start: RSS 2537.39 MB
[2025-08-22 14:16:51] [warning] CPU Memory at Target detection complete: RSS 2597.76 MB
[2025-08-22 14:16:51] [warning] CPU Memory at Before TargetTracking: RSS 2749.58 MB
=== Tracking ===
  id=0 x=1341.26 y=-4984.77 z=442.545
  id=1 x=1350.14 y=-5017.76 z=245.048
  id=2 x=1242.44 y=-4617.5 z=418.348
=== Tracking ===
  id=0 x=1335.48 y=-4963.28 z=440.637
  id=1 x=1344.24 y=-4995.86 z=243.978
  id=2 x=1236.61 y=-4595.84 z=416.385
=== Tracking ===
  id=0 x=1329.7 y=-4941.79 z=438.729
  id=1 x=1338.35 y=-4973.96 z=242.909
  id=2 x=1230.78 y=-4574.17 z=414.422
=== Tracking ===
  id=0 x=1323.91 y=-4920.29 z=436.821
  id=1 x=1332.46 y=-4952.06 z=241.839
  id=2 x=1224.95 y=-4552.51 z=412.459
=== Tracking ===
  id=0 x=1318.13 y=-4898.8 z=434.913
  id=1 x=1326.57 y=-4930.16 z=240.77
  id=2 x=1219.12 y=-4530.84 z=410.496
=== Tracking ===
  id=0 x=1312.35 y=-4877.31 z=433.005
  id=1 x=1320.67 y=-4908.26 z=239.7
  id=2 x=1213.29 y=-4509.18 z=408.533
[2025-08-22 14:16:58] [warning] CPU Memory at After TargetTracking: RSS 2855.86 MB
[2025-08-22 14:16:59] [warning] CPU Memory at Target detection start: RSS 2848.13 MB
[2025-08-22 14:17:00] [warning] 无效帧:33241 模型没有检测到目标，跳过处理
[2025-08-22 14:17:03] [warning] CPU Memory at Before TargetTracking: RSS 2856.63 MB
[2025-08-22 14:17:03] [warning] 没有检测到目标，跳过跟踪
[2025-08-22 14:17:05] [warning] CPU Memory at Target detection start: RSS 2927.71 MB
[2025-08-22 14:17:09] [warning] 无效帧:33242 模型没有检测到目标，跳过处理
[2025-08-22 14:17:09] [warning] CPU Memory at Before TargetTracking: RSS 3050.06 MB
[2025-08-22 14:17:09] [warning] 没有检测到目标，跳过跟踪
[2025-08-22 14:17:12] [warning] CPU Memory at Target detection start: RSS 2818.98 MB
[2025-08-22 14:17:14] [warning] CPU Memory at Target detection complete: RSS 3051.39 MB
[2025-08-22 14:17:17] [warning] CPU Memory at Before TargetTracking: RSS 2947.72 MB
=== Tracking ===
  id=0 x=1352.32 y=-5025.89 z=428.56
=== Tracking ===
  id=0 x=1350.06 y=-5017.47 z=426.497
=== Tracking ===
  id=0 x=1347.8 y=-5009.06 z=424.433
=== Tracking ===
  id=0 x=1345.53 y=-5000.65 z=422.37
=== Tracking ===
  id=0 x=1343.27 y=-4992.23 z=420.307
=== Tracking ===
  id=0 x=1341.01 y=-4983.82 z=418.243
[2025-08-22 14:17:17] [warning] CPU Memory at After TargetTracking: RSS 2949.46 MB
==3059068== 
==3059068== HEAP SUMMARY:
==3059068==     in use at exit: 1,409,792 bytes in 345 blocks
==3059068==   total heap usage: 298,920 allocs, 298,575 frees, 4,589,813,459 bytes allocated
==3059068== 
==3059068== 4 bytes in 1 blocks are still reachable in loss record 1 of 184
==3059068==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5288DFDD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B22436: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B276EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52896FF1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0E673: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
}
==3059068== 4 bytes in 1 blocks are still reachable in loss record 2 of 184
==3059068==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5288DFF1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B22436: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B276EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52896FF1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0E673: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
}
==3059068== 8 bytes in 1 blocks are still reachable in loss record 3 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x15F3384C: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==3059068==    by 0x15F460BA: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==3059068==    by 0x15F31FE1: ??? (in /usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0
   obj:/usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0
   obj:/usr/lib/x86_64-linux-gnu/libgomp.so.1.0.0
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 8 bytes in 1 blocks are still reachable in loss record 4 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27861241: cv::details::getTlsAbstraction() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786135B: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27868DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27869E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv7detailsL17getTlsAbstractionEv
   fun:_ZN2cv7detailsL13getTlsStorageEv
   fun:_ZN2cv16TLSDataContainerC1Ev
   fun:_ZN2cv5utils5trace7details12TraceManagerC1Ev
   fun:_ZN2cv5utils5trace7details15getTraceManagerEv
   fun:_ZN2cv5utils5trace7details12TraceManager11isActivatedEv
   fun:_ZN2cv5utils5trace7details6RegionC1ERKNS3_21LocationStaticStorageE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
}
==3059068== 8 bytes in 1 blocks are still reachable in loss record 5 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27881E22: void std::vector<cv::utils::logging::LogTagManager::NamePartInfo, std::allocator<cv::utils::logging::LogTagManager::NamePartInfo> >::_M_realloc_insert<cv::utils::logging::LogTagManager::NamePartInfo>(__gnu_cxx::__normal_iterator<cv::utils::logging::LogTagManager::NamePartInfo*, std::vector<cv::utils::logging::LogTagManager::NamePartInfo, std::allocator<cv::utils::logging::LogTagManager::NamePartInfo> > >, cv::utils::logging::LogTagManager::NamePartInfo&&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27882A8D: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupNameParts(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, std::vector<unsigned long, std::allocator<unsigned long> >&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278833AB: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt6vectorIN2cv5utils7logging13LogTagManager12NamePartInfoESaIS4_EE17_M_realloc_insertIJS4_EEEvN9__gnu_cxx17__normal_iteratorIPS4_S6_EEDpOT_
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable29internal_addOrLookupNamePartsERKSt6vectorINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESaISA_EERS4_ImSaImEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 8 bytes in 1 blocks are still reachable in loss record 6 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5287303E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 8 bytes in 1 blocks are still reachable in loss record 7 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52873786: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 8 bytes in 1 blocks are still reachable in loss record 8 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x277314E9: cv::Mat::getStdAllocator() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27731574: cv::Mat::getDefaultAllocator() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27732BB9: cv::Mat::create(int, int const*, int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27732FA6: cv::Mat::create(int, int, int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C189: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068==    by 0x29AC3A: std::__invoke_result<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >::type std::__invoke<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:96)
==3059068==    by 0x29A8E0: void std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::_M_invoke<0ul, 1ul>(std::_Index_tuple<0ul, 1ul>) (std_thread.h:259)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv3Mat15getStdAllocatorEv
   fun:_ZN2cv3Mat19getDefaultAllocatorEv
   fun:_ZN2cv3Mat6createEiPKii
   fun:_ZN2cv3Mat6createEiii
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt8__invokeIPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEENSt15__invoke_resultIT_JDpT0_EE4typeEOS7_DpOS8_
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEE9_M_invokeIJLm0ELm1EEEEvSt12_Index_tupleIJXspT_EEE
}
==3059068== 12 bytes in 1 blocks are still reachable in loss record 9 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x524358E: strdup (strdup.c:42)
==3059068==    by 0x2794EA95: __itt_domain_create_init_3_0 (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27865FD8: cv::utils::trace::details::isITTEnabled() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786893C: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27868DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27869E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   fun:strdup
   fun:__itt_domain_create_init_3_0
   fun:_ZN2cv5utils5trace7detailsL12isITTEnabledEv
   fun:_ZN2cv5utils5trace7details12TraceManagerC1Ev
   fun:_ZN2cv5utils5trace7details15getTraceManagerEv
   fun:_ZN2cv5utils5trace7details12TraceManager11isActivatedEv
   fun:_ZN2cv5utils5trace7details6RegionC1ERKNS3_21LocationStaticStorageE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 10 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x40085DB: malloc (rtld-malloc.h:56)
==3059068==    by 0x40085DB: decompose_rpath (dl-load.c:644)
==3059068==    by 0x400ABF5: cache_rpath (dl-load.c:696)
==3059068==    by 0x400ABF5: cache_rpath (dl-load.c:677)
==3059068==    by 0x400ABF5: _dl_map_object (dl-load.c:2165)
==3059068==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400E34D: _dl_open (dl-open.c:883)
==3059068==    by 0x522B63B: dlopen_doit (dlopen.c:56)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x530FB62: _dl_catch_error (dl-error-skeleton.c:227)
==3059068==    by 0x522B12D: _dlerror_run (dlerror.c:138)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   fun:malloc
   fun:decompose_rpath
   fun:cache_rpath
   fun:cache_rpath
   fun:_dl_map_object
   fun:dl_open_worker_begin
   fun:_dl_catch_exception
   fun:dl_open_worker
   fun:_dl_catch_exception
   fun:_dl_open
   fun:dlopen_doit
   fun:_dl_catch_exception
   fun:_dl_catch_error
   fun:_dlerror_run
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 11 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2788385B: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 12 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x278818E2: void std::vector<cv::utils::logging::LogTagManager::FullNameInfo, std::allocator<cv::utils::logging::LogTagManager::FullNameInfo> >::_M_realloc_insert<cv::utils::logging::LogTagManager::FullNameInfo>(__gnu_cxx::__normal_iterator<cv::utils::logging::LogTagManager::FullNameInfo*, std::vector<cv::utils::logging::LogTagManager::FullNameInfo, std::allocator<cv::utils::logging::LogTagManager::FullNameInfo> > >, cv::utils::logging::LogTagManager::FullNameInfo&&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2788291C: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupFullName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278832D1: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt6vectorIN2cv5utils7logging13LogTagManager12FullNameInfoESaIS4_EE17_M_realloc_insertIJS4_EEEvN9__gnu_cxx17__normal_iteratorIPS4_S6_EEDpOT_
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable28internal_addOrLookupFullNameERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 13 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5278C0C2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528730BF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 14 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5278C0C2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873147: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 15 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5278C0C2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5287316D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 16 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A5FD78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E94F4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 17 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528DE9DD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529A182E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D99CE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x522FAC2: start_thread (pthread_create.c:442)
==3059068==    by 0x52C0A03: clone (clone.S:100)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:start_thread
   fun:clone
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 18 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27864C59: cv::getCoreTlsData() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27848EFC: cv::theRNG() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27808623: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068==    by 0x29AC3A: std::__invoke_result<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >::type std::__invoke<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:96)
==3059068==    by 0x29A8E0: void std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::_M_invoke<0ul, 1ul>(std::_Index_tuple<0ul, 1ul>) (std_thread.h:259)
==3059068==    by 0x299F3D: std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::operator()() (std_thread.h:266)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv14getCoreTlsDataEv
   fun:_ZN2cv6theRNGEv
   fun:_ZN2cv13parallel_for_ERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt8__invokeIPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEENSt15__invoke_resultIT_JDpT0_EE4typeEOS7_DpOS8_
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEE9_M_invokeIJLm0ELm1EEEEvSt12_Index_tupleIJXspT_EEE
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEEclEv
}
==3059068== 16 bytes in 1 blocks are still reachable in loss record 19 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27864BBC: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27865C09: cv::TLSDataAccumulator<cv::utils::trace::details::TraceManagerThreadLocal>::createDataInstance() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27863E61: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27808646: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068==    by 0x29AC3A: std::__invoke_result<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >::type std::__invoke<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:96)
==3059068==    by 0x29A8E0: void std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::_M_invoke<0ul, 1ul>(std::_Index_tuple<0ul, 1ul>) (std_thread.h:259)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv5utils11getThreadIDEv
   fun:_ZNK2cv18TLSDataAccumulatorINS_5utils5trace7details23TraceManagerThreadLocalEE18createDataInstanceEv
   fun:_ZNK2cv16TLSDataContainer7getDataEv
   fun:_ZN2cv13parallel_for_ERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt8__invokeIPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEENSt15__invoke_resultIT_JDpT0_EE4typeEOS7_DpOS8_
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEE9_M_invokeIJLm0ELm1EEEEvSt12_Index_tupleIJXspT_EEE
}
==3059068== 24 bytes in 1 blocks are still reachable in loss record 20 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x529A114B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 21 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x4013E4D: malloc (rtld-malloc.h:56)
==3059068==    by 0x4013E4D: allocate_dtv_entry (dl-tls.c:684)
==3059068==    by 0x4013E4D: allocate_and_init (dl-tls.c:709)
==3059068==    by 0x4013E4D: tls_get_addr_tail (dl-tls.c:907)
==3059068==    by 0x401820B: __tls_get_addr (tls_get_addr.S:55)
==3059068==    by 0x55734A9C: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x55734972: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x55723F32: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x546AD14D: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x56920775: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x545DA8DA: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x6: ???
==3059068==    by 0x4006439: call_init.part.0 (dl-init.c:56)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   fun:malloc
   fun:allocate_dtv_entry
   fun:allocate_and_init
   fun:tls_get_addr_tail
   fun:__tls_get_addr
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:*
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 22 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x278831D4: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2788344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable26internal_addCrossReferenceEmRKSt6vectorImSaImEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 23 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27883215: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2788344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable26internal_addCrossReferenceEmRKSt6vectorImSaImEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 24 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5286C72B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872526: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 25 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5286C747: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872526: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 26 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52B0D8B2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5287323A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 27 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5287325F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 28 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873A99: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 29 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873ABB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 30 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE945: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 31 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A5EF98: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9BF4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 32 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6495DCD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C1699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 33 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FC44: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FDD3: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782EF72: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x786F015: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x76AA074: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 34 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x661DA71: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C6414: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 35 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66BD145: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C135C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 36 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6970317: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x699B664: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 37 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6607B87: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C135C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 38 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x644ACB2: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C1699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 39 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6349915: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C77E4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 32 bytes in 1 blocks are still reachable in loss record 40 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBBA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6783B7A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6786BA9: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 33 bytes in 1 blocks are still reachable in loss record 41 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52B0DADC: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5287323A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 35 bytes in 1 blocks are still reachable in loss record 42 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x40271DF: malloc (rtld-malloc.h:56)
==3059068==    by 0x40271DF: strdup (strdup.c:42)
==3059068==    by 0x4016A66: _dl_load_cache_lookup (dl-cache.c:527)
==3059068==    by 0x400A981: _dl_map_object (dl-load.c:2193)
==3059068==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400E34D: _dl_open (dl-open.c:883)
==3059068==    by 0x522B63B: dlopen_doit (dlopen.c:56)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x530FB62: _dl_catch_error (dl-error-skeleton.c:227)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   fun:malloc
   fun:strdup
   fun:_dl_load_cache_lookup
   fun:_dl_map_object
   fun:dl_open_worker_begin
   fun:_dl_catch_exception
   fun:dl_open_worker
   fun:_dl_catch_exception
   fun:_dl_open
   fun:dlopen_doit
   fun:_dl_catch_exception
   fun:_dl_catch_error
}
==3059068== 39 bytes in 1 blocks are still reachable in loss record 43 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x4007173: malloc (rtld-malloc.h:56)
==3059068==    by 0x4007173: open_path (dl-load.c:1977)
==3059068==    by 0x400A6B0: _dl_map_object (dl-load.c:2158)
==3059068==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400E34D: _dl_open (dl-open.c:883)
==3059068==    by 0x522B63B: dlopen_doit (dlopen.c:56)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x530FB62: _dl_catch_error (dl-error-skeleton.c:227)
==3059068==    by 0x522B12D: _dlerror_run (dlerror.c:138)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   fun:malloc
   fun:open_path
   fun:_dl_map_object
   fun:dl_open_worker_begin
   fun:_dl_catch_exception
   fun:dl_open_worker
   fun:_dl_catch_exception
   fun:_dl_open
   fun:dlopen_doit
   fun:_dl_catch_exception
   fun:_dl_catch_error
   fun:_dlerror_run
}
==3059068== 40 bytes in 1 blocks are still reachable in loss record 44 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2785DCC5: cv::getInitializationMutex() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27865F70: cv::utils::trace::details::isITTEnabled() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786893C: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27868DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27869E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv22getInitializationMutexEv
   fun:_ZN2cv5utils5trace7detailsL12isITTEnabledEv
   fun:_ZN2cv5utils5trace7details12TraceManagerC1Ev
   fun:_ZN2cv5utils5trace7details15getTraceManagerEv
   fun:_ZN2cv5utils5trace7details12TraceManager11isActivatedEv
   fun:_ZN2cv5utils5trace7details6RegionC1ERKNS3_21LocationStaticStorageE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
}
==3059068== 48 bytes in 1 blocks are still reachable in loss record 45 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2794EA79: __itt_domain_create_init_3_0 (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27865FD8: cv::utils::trace::details::isITTEnabled() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786893C: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27868DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27869E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   fun:__itt_domain_create_init_3_0
   fun:_ZN2cv5utils5trace7detailsL12isITTEnabledEv
   fun:_ZN2cv5utils5trace7details12TraceManagerC1Ev
   fun:_ZN2cv5utils5trace7details15getTraceManagerEv
   fun:_ZN2cv5utils5trace7details12TraceManager11isActivatedEv
   fun:_ZN2cv5utils5trace7details6RegionC1ERKNS3_21LocationStaticStorageE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 46 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x278825C0: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278828FD: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupFullName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278832D1: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt10_HashtableINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESt4pairIKS5_mESaIS8_ENSt8__detail10_Select1stESt8equal_toIS5_ESt4hashIS5_ENSA_18_Mod_range_hashingENSA_20_Default_ranged_hashENSA_20_Prime_rehash_policyENSA_17_Hashtable_traitsILb1ELb0ELb1EEEE10_M_emplaceIJRS7_RKmEEES6_INSA_14_Node_iteratorIS8_Lb0ELb1EEEbESt17integral_constantIbLb1EEDpOT_.isra.0
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable28internal_addOrLookupFullNameERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 47 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x278825C0: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27882A54: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupNameParts(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, std::vector<unsigned long, std::allocator<unsigned long> >&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278833AB: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt10_HashtableINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESt4pairIKS5_mESaIS8_ENSt8__detail10_Select1stESt8equal_toIS5_ESt4hashIS5_ENSA_18_Mod_range_hashingENSA_20_Default_ranged_hashENSA_20_Prime_rehash_policyENSA_17_Hashtable_traitsILb1ELb0ELb1EEEE10_M_emplaceIJRS7_RKmEEES6_INSA_14_Node_iteratorIS8_Lb0ELb1EEEbESt17integral_constantIbLb1EEDpOT_.isra.0
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable29internal_addOrLookupNamePartsERKSt6vectorINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESaISA_EERS4_ImSaImEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 48 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5286E9CB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB264: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 49 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5286EA6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB264: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 50 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5286EAF6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB264: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 51 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528E1909: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 52 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52872E85: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 53 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528DCE2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5287321F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 54 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A5F1CF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A51A5B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 55 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A5F1CF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A51A82: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 56 bytes in 1 blocks are still reachable in loss record 56 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528DCE2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873769: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 64 bytes in 1 blocks are still reachable in loss record 57 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A6E2D0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A57D6F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5931A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 64 bytes in 1 blocks are still reachable in loss record 58 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A6E2D0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A57F80: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A594B7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 64 bytes in 1 blocks are still reachable in loss record 59 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52872F0E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 64 bytes in 1 blocks are still reachable in loss record 60 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52872FFB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 64 bytes in 1 blocks are still reachable in loss record 61 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x529E9E35: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 64 bytes in 1 blocks are still reachable in loss record 62 of 184
==3059068==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5299B343: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529A122C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 63 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297C66F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872A6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 64 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52B3305D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A70574: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A509A6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A48E6A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A61B78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528E177C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 65 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528DCEED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529A126E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 66 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6495DCD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C1699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66DC860: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 67 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FC44: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FDD3: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782EF72: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x786F015: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x76AA074: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x76A85F5: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 68 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x661DA71: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C6414: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C64F0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 69 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66BD145: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C135C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66D8DB5: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 70 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6970317: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x699B664: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x691A417: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 71 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6607B87: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C135C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66DC860: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 72 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x644ACB2: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C1699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66DC860: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 73 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6349915: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C77E4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66DDDCE: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are still reachable in loss record 74 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CFFEE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6783B7A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6786BA9: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6782A47: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 72 bytes in 1 blocks are possibly lost in loss record 75 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A143B2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CB029: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528C6614: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873B70: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 72 bytes in 1 blocks are possibly lost in loss record 76 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528D0F40: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D1441: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D340A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 73 bytes in 1 blocks are still reachable in loss record 77 of 184
==3059068==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52B0D842: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5286C241: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872526: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:realloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 74 bytes in 2 blocks are still reachable in loss record 78 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x400DD20: malloc (rtld-malloc.h:56)
==3059068==    by 0x400DD20: _dl_new_object (dl-object.c:199)
==3059068==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==3059068==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==3059068==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400E34D: _dl_open (dl-open.c:883)
==3059068==    by 0x522B63B: dlopen_doit (dlopen.c:56)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x530FB62: _dl_catch_error (dl-error-skeleton.c:227)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   fun:malloc
   fun:_dl_new_object
   fun:_dl_map_object_from_fd
   fun:_dl_map_object
   fun:dl_open_worker_begin
   fun:_dl_catch_exception
   fun:dl_open_worker
   fun:_dl_catch_exception
   fun:_dl_open
   fun:dlopen_doit
   fun:_dl_catch_exception
   fun:_dl_catch_error
}
==3059068== 80 bytes in 1 blocks are still reachable in loss record 79 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A62601: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872B54: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 88 bytes in 1 blocks are still reachable in loss record 80 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x529A1163: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 88 bytes in 1 blocks are still reachable in loss record 81 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528749E0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 96 bytes in 1 blocks are still reachable in loss record 82 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27861321: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27868DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27869E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv7detailsL13getTlsStorageEv
   fun:_ZN2cv16TLSDataContainerC1Ev
   fun:_ZN2cv5utils5trace7details12TraceManagerC1Ev
   fun:_ZN2cv5utils5trace7details15getTraceManagerEv
   fun:_ZN2cv5utils5trace7details12TraceManager11isActivatedEv
   fun:_ZN2cv5utils5trace7details6RegionC1ERKNS3_21LocationStaticStorageE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
}
==3059068== 104 bytes in 1 blocks are still reachable in loss record 83 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2788248D: std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278827D4: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278828FD: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupFullName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278832D1: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt10_HashtableINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESt4pairIKS5_mESaIS8_ENSt8__detail10_Select1stESt8equal_toIS5_ESt4hashIS5_ENSA_18_Mod_range_hashingENSA_20_Default_ranged_hashENSA_20_Prime_rehash_policyENSA_17_Hashtable_traitsILb1ELb0ELb1EEEE9_M_rehashEmRKm
   fun:_ZNSt10_HashtableINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESt4pairIKS5_mESaIS8_ENSt8__detail10_Select1stESt8equal_toIS5_ESt4hashIS5_ENSA_18_Mod_range_hashingENSA_20_Default_ranged_hashENSA_20_Prime_rehash_policyENSA_17_Hashtable_traitsILb1ELb0ELb1EEEE10_M_emplaceIJRS7_RKmEEES6_INSA_14_Node_iteratorIS8_Lb0ELb1EEEbESt17integral_constantIbLb1EEDpOT_.isra.0
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable28internal_addOrLookupFullNameERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 104 bytes in 1 blocks are still reachable in loss record 84 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2788248D: std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278827D4: std::pair<std::__detail::_Node_iterator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, false, true>, bool> std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, unsigned long> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_emplace<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&>(std::integral_constant<bool, true>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, unsigned long const&) [clone .isra.0] (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27882A54: cv::utils::logging::LogTagManager::NameTable::internal_addOrLookupNameParts(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, std::vector<unsigned long, std::allocator<unsigned long> >&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278833AB: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt10_HashtableINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESt4pairIKS5_mESaIS8_ENSt8__detail10_Select1stESt8equal_toIS5_ESt4hashIS5_ENSA_18_Mod_range_hashingENSA_20_Default_ranged_hashENSA_20_Prime_rehash_policyENSA_17_Hashtable_traitsILb1ELb0ELb1EEEE9_M_rehashEmRKm
   fun:_ZNSt10_HashtableINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESt4pairIKS5_mESaIS8_ENSt8__detail10_Select1stESt8equal_toIS5_ESt4hashIS5_ENSA_18_Mod_range_hashingENSA_20_Default_ranged_hashENSA_20_Prime_rehash_policyENSA_17_Hashtable_traitsILb1ELb0ELb1EEEE10_M_emplaceIJRS7_RKmEEES6_INSA_14_Node_iteratorIS8_Lb0ELb1EEEbESt17integral_constantIbLb1EEDpOT_.isra.0
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable29internal_addOrLookupNamePartsERKSt6vectorINSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESaISA_EERS4_ImSaImEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 104 bytes in 1 blocks are still reachable in loss record 85 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27882F2C: std::_Hashtable<unsigned long, std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, std::allocator<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> > >, std::__detail::_Select1st, std::equal_to<unsigned long>, std::hash<unsigned long>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, false> >::_M_insert_multi_node(std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*, unsigned long, std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278831FD: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2788344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt10_HashtableImSt4pairIKmS0_ImmEESaIS3_ENSt8__detail10_Select1stESt8equal_toImESt4hashImENS5_18_Mod_range_hashingENS5_20_Default_ranged_hashENS5_20_Prime_rehash_policyENS5_17_Hashtable_traitsILb0ELb0ELb0EEEE20_M_insert_multi_nodeEPNS5_10_Hash_nodeIS3_Lb0EEEmSJ_
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable26internal_addCrossReferenceEmRKSt6vectorImSaImEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 104 bytes in 1 blocks are still reachable in loss record 86 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27882F2C: std::_Hashtable<unsigned long, std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, std::allocator<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> > >, std::__detail::_Select1st, std::equal_to<unsigned long>, std::hash<unsigned long>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, false> >::_M_insert_multi_node(std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*, unsigned long, std::__detail::_Hash_node<std::pair<unsigned long const, std::pair<unsigned long, unsigned long> >, false>*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2788323C: cv::utils::logging::LogTagManager::NameTable::internal_addCrossReference(unsigned long, std::vector<unsigned long, std::allocator<unsigned long> > const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2788344F: cv::utils::logging::LogTagManager::NameTable::addOrLookupFullName(cv::utils::logging::LogTagManager::FullNameLookupResult&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278835A0: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt10_HashtableImSt4pairIKmS0_ImmEESaIS3_ENSt8__detail10_Select1stESt8equal_toImESt4hashImENS5_18_Mod_range_hashingENS5_20_Default_ranged_hashENS5_20_Prime_rehash_policyENS5_17_Hashtable_traitsILb0ELb0ELb0EEEE20_M_insert_multi_nodeEPNS5_10_Hash_nodeIS3_Lb0EEEmSJ_
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable26internal_addCrossReferenceEmRKSt6vectorImSaImEE
   fun:_ZN2cv5utils7logging13LogTagManager9NameTable19addOrLookupFullNameERNS2_20FullNameLookupResultE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 108 bytes in 27 blocks are still reachable in loss record 87 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27862275: cv::TLSData<cv::(anonymous namespace)::ThreadID>::createDataInstance() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27863E61: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27864B8E: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781B48E: cv::WorkerThread::thread_loop_wrapper(void*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x522FAC2: start_thread (pthread_create.c:442)
==3059068==    by 0x52C0A03: clone (clone.S:100)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNK2cv7TLSDataINS_12_GLOBAL__N_18ThreadIDEE18createDataInstanceEv
   fun:_ZNK2cv16TLSDataContainer7getDataEv
   fun:_ZN2cv5utils11getThreadIDEv
   fun:_ZN2cv12WorkerThread19thread_loop_wrapperEPv
   fun:start_thread
   fun:clone
}
==3059068== 120 bytes in 1 blocks are still reachable in loss record 88 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x529E9491: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 89 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528CB932: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872DE2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 90 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528E1A48: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 91 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873AF8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 92 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873B27: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 93 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A51A5B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 94 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A51A82: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 95 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873A3A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 96 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873628: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 97 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE916: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 98 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE934: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 99 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873C59: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 100 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D00: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 101 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 102 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B4E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 103 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 104 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F3A0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B8B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 105 of 184
==3059068==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5299B327: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529A122C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 128 bytes in 1 blocks are still reachable in loss record 106 of 184
==3059068==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5299B35D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529A122C: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 152 bytes in 1 blocks are still reachable in loss record 107 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873628: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 152 bytes in 1 blocks are still reachable in loss record 108 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297F036: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE975: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
}
==3059068== 152 bytes in 1 blocks are still reachable in loss record 109 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297EFDB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE975: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
}
==3059068== 152 bytes in 1 blocks are still reachable in loss record 110 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B8B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 152 bytes in 1 blocks are still reachable in loss record 111 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297F036: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9519: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
}
==3059068== 152 bytes in 1 blocks are still reachable in loss record 112 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297DFC0: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297EFDB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297F0ED: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9519: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9CE5: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 113 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528E1A48: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 114 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873AF8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 115 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873B27: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 116 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A51A5B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 117 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5F4A8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A5FBB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A51A82: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528735D7: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 118 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873A3A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 119 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE916: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 120 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE934: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 121 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873C59: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 122 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D00: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 123 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B2E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 124 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B4E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 152 bytes in 1 blocks are possibly lost in loss record 125 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297F438: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529E9B6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 160 bytes in 1 blocks are still reachable in loss record 126 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5287325F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 176 bytes in 1 blocks are still reachable in loss record 127 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2781DE34: cv::parallel_for_pthreads(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068==    by 0x29AC3A: std::__invoke_result<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >::type std::__invoke<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:96)
==3059068==    by 0x29A8E0: void std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::_M_invoke<0ul, 1ul>(std::_Index_tuple<0ul, 1ul>) (std_thread.h:259)
==3059068==    by 0x299F3D: std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::operator()() (std_thread.h:266)
==3059068==    by 0x294F35: std::thread::_State_impl<std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > > >::_M_run() (std_thread.h:211)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv21parallel_for_pthreadsERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv13parallel_for_ERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt8__invokeIPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEENSt15__invoke_resultIT_JDpT0_EE4typeEOS7_DpOS8_
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEE9_M_invokeIJLm0ELm1EEEEvSt12_Index_tupleIJXspT_EEE
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEEclEv
   fun:_ZNSt6thread11_State_implINS_8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS3_EEEEEE6_M_runEv
}
==3059068== 184 bytes in 1 blocks are still reachable in loss record 128 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x278839D7: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 192 bytes in 1 blocks are still reachable in loss record 129 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x13C12294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13C5E8B0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13BE7065: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 192 bytes in 1 blocks are still reachable in loss record 130 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x13C12294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13C5EC30: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13BE70A6: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 192 bytes in 1 blocks are still reachable in loss record 131 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x13C12294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13C62ED0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13BE7205: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 192 bytes in 1 blocks are still reachable in loss record 132 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x13C12294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13C63250: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13BE7246: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 224 bytes in 1 blocks are still reachable in loss record 133 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528E1874: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 240 bytes in 1 blocks are still reachable in loss record 134 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A52A46: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528DEF73: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 248 bytes in 1 blocks are still reachable in loss record 135 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5287395A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 256 bytes in 1 blocks are still reachable in loss record 136 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2786137F: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27868DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27869E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv7detailsL13getTlsStorageEv
   fun:_ZN2cv16TLSDataContainerC1Ev
   fun:_ZN2cv5utils5trace7details12TraceManagerC1Ev
   fun:_ZN2cv5utils5trace7details15getTraceManagerEv
   fun:_ZN2cv5utils5trace7details12TraceManager11isActivatedEv
   fun:_ZN2cv5utils5trace7details6RegionC1ERKNS3_21LocationStaticStorageE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
}
==3059068== 256 bytes in 1 blocks are still reachable in loss record 137 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27861404: cv::details::getTlsStorage() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786496F: cv::TLSDataContainer::TLSDataContainer() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278684E6: cv::utils::trace::details::TraceManager::TraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27868DD9: cv::utils::trace::details::getTraceManager() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27869E30: cv::utils::trace::details::TraceManager::isActivated() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2786AE4A: cv::utils::trace::details::Region::Region(cv::utils::trace::details::Region::LocationStaticStorage const&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278834E9: cv::utils::logging::LogTagManager::assign(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::utils::logging::LogTag*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27883A7C: cv::utils::logging::LogTagManager::LogTagManager(cv::utils::logging::LogLevel) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x276E962D: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv7detailsL13getTlsStorageEv
   fun:_ZN2cv16TLSDataContainerC1Ev
   fun:_ZN2cv5utils5trace7details12TraceManagerC1Ev
   fun:_ZN2cv5utils5trace7details15getTraceManagerEv
   fun:_ZN2cv5utils5trace7details12TraceManager11isActivatedEv
   fun:_ZN2cv5utils5trace7details6RegionC1ERKNS3_21LocationStaticStorageE
   fun:_ZN2cv5utils7logging13LogTagManager6assignERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEEPNS1_6LogTagE
   fun:_ZN2cv5utils7logging13LogTagManagerC1ENS1_8LogLevelE
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
}
==3059068== 264 bytes in 1 blocks are still reachable in loss record 138 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5287327E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 272 bytes in 1 blocks are still reachable in loss record 139 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x529A1064: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 140 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6495DCD: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C1699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 141 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FC44: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FDD3: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782EF72: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x786F015: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x76AA074: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 142 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x661DA71: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C6414: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 143 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x66BD145: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C135C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 144 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6970317: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x699B664: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 145 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6607B87: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C135C: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 146 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x644ACB2: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C1699: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 147 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6349915: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x62C77E4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 148 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D0008: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527ACCE6: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x527AD8AE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x784EFC4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x784FB97: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x7854AE4: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x782E3A0: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x788B775: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6783B7A: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068==    by 0x6786BA9: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer.so.8.6.1
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 149 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CCB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297D9EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B43453: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528AA8EC: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52834C64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5294C26F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAD5FB: __cudart1628 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D8E410: __cudart745 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DC8D04: cudaDeviceReset (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D28C55: ReleaseAllResources() (libSCR_5000_Alg.cpp:996)
==3059068==    by 0x226BB8: main (test_algorithm.cpp:577)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart1628
   fun:__cudart745
   fun:cudaDeviceReset
   fun:_Z19ReleaseAllResourcesv
   fun:main
}
==3059068== 320 bytes in 1 blocks are still reachable in loss record 150 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CCB4: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297D9EE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528AB38B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52834C64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5294C26F: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAD5FB: __cudart1628 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D8E410: __cudart745 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DC8D04: cudaDeviceReset (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D28C55: ReleaseAllResources() (libSCR_5000_Alg.cpp:996)
==3059068==    by 0x226BB8: main (test_algorithm.cpp:577)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart1628
   fun:__cudart745
   fun:cudaDeviceReset
   fun:_Z19ReleaseAllResourcesv
   fun:main
}
==3059068== 336 bytes in 1 blocks are still reachable in loss record 151 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x276E961C: cv::utils::logging::internal::getGlobalLoggingInitStruct() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2762C1AE: _GLOBAL__sub_I_logger.cpp (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv5utils7logging8internalL26getGlobalLoggingInitStructEv
   fun:_GLOBAL__sub_I_logger.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 336 bytes in 1 blocks are still reachable in loss record 152 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x13C09170: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13BE38F5: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 432 bytes in 1 blocks are possibly lost in loss record 153 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x40147D9: calloc (rtld-malloc.h:44)
==3059068==    by 0x40147D9: allocate_dtv (dl-tls.c:375)
==3059068==    by 0x40147D9: _dl_allocate_tls (dl-tls.c:634)
==3059068==    by 0x52307B4: allocate_stack (allocatestack.c:430)
==3059068==    by 0x52307B4: pthread_create@@GLIBC_2.34 (pthread_create.c:647)
==3059068==    by 0x528DCF52: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x529A126E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52874ACE: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   fun:calloc
   fun:allocate_dtv
   fun:_dl_allocate_tls
   fun:allocate_stack
   fun:pthread_create@@GLIBC_2.34
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 440 bytes in 1 blocks are still reachable in loss record 154 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528CE945: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528D3424: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528745CD: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 464 bytes in 1 blocks are still reachable in loss record 155 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52B0DEB2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 512 bytes in 1 blocks are still reachable in loss record 156 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52872F65: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 512 bytes in 1 blocks are still reachable in loss record 157 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52872FB2: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 512 bytes in 1 blocks are still reachable in loss record 158 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2781CBBD: void std::vector<cv::Ptr<cv::WorkerThread>, std::allocator<cv::Ptr<cv::WorkerThread> > >::_M_realloc_insert<cv::Ptr<cv::WorkerThread> >(__gnu_cxx::__normal_iterator<cv::Ptr<cv::WorkerThread>*, std::vector<cv::Ptr<cv::WorkerThread>, std::allocator<cv::Ptr<cv::WorkerThread> > > >, cv::Ptr<cv::WorkerThread>&&) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781CD1B: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068==    by 0x29AC3A: std::__invoke_result<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >::type std::__invoke<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:96)
==3059068==    by 0x29A8E0: void std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::_M_invoke<0ul, 1ul>(std::_Index_tuple<0ul, 1ul>) (std_thread.h:259)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNSt6vectorIN2cv3PtrINS0_12WorkerThreadEEESaIS3_EE17_M_realloc_insertIJS3_EEEvN9__gnu_cxx17__normal_iteratorIPS3_S5_EEDpOT_
   fun:_ZN2cv10ThreadPool12reconfigure_Ej
   fun:_ZN2cv10ThreadPool3runERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv13parallel_for_ERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt8__invokeIPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEENSt15__invoke_resultIT_JDpT0_EE4typeEOS7_DpOS8_
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEE9_M_invokeIJLm0ELm1EEEEvSt12_Index_tupleIJXspT_EEE
}
==3059068== 552 bytes in 2 blocks are still reachable in loss record 159 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x40162DC: calloc (rtld-malloc.h:44)
==3059068==    by 0x40162DC: _dl_check_map_versions (dl-version.c:273)
==3059068==    by 0x400ED13: dl_open_worker_begin (dl-open.c:600)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400E34D: _dl_open (dl-open.c:883)
==3059068==    by 0x522B63B: dlopen_doit (dlopen.c:56)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x530FB62: _dl_catch_error (dl-error-skeleton.c:227)
==3059068==    by 0x522B12D: _dlerror_run (dlerror.c:138)
==3059068==    by 0x522B6C7: dlopen_implementation (dlopen.c:71)
==3059068==    by 0x522B6C7: dlopen@@GLIBC_2.34 (dlopen.c:81)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   fun:calloc
   fun:_dl_check_map_versions
   fun:dl_open_worker_begin
   fun:_dl_catch_exception
   fun:dl_open_worker
   fun:_dl_catch_exception
   fun:_dl_open
   fun:dlopen_doit
   fun:_dl_catch_exception
   fun:_dl_catch_error
   fun:_dlerror_run
   fun:dlopen_implementation
   fun:dlopen@@GLIBC_2.34
}
==3059068== 640 bytes in 1 blocks are still reachable in loss record 160 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873ABB: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 648 bytes in 27 blocks are still reachable in loss record 161 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2781CCEC: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068==    by 0x29AC3A: std::__invoke_result<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >::type std::__invoke<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:96)
==3059068==    by 0x29A8E0: void std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::_M_invoke<0ul, 1ul>(std::_Index_tuple<0ul, 1ul>) (std_thread.h:259)
==3059068==    by 0x299F3D: std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::operator()() (std_thread.h:266)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv10ThreadPool12reconfigure_Ej
   fun:_ZN2cv10ThreadPool3runERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv13parallel_for_ERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt8__invokeIPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEENSt15__invoke_resultIT_JDpT0_EE4typeEOS7_DpOS8_
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEE9_M_invokeIJLm0ELm1EEEEvSt12_Index_tupleIJXspT_EEE
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEEclEv
}
==3059068== 864 bytes in 27 blocks are still reachable in loss record 162 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27863EDA: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27864B8E: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781B48E: cv::WorkerThread::thread_loop_wrapper(void*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x522FAC2: start_thread (pthread_create.c:442)
==3059068==    by 0x52C0A03: clone (clone.S:100)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNK2cv16TLSDataContainer7getDataEv
   fun:_ZN2cv5utils11getThreadIDEv
   fun:_ZN2cv12WorkerThread19thread_loop_wrapperEPv
   fun:start_thread
   fun:clone
}
==3059068== 920 bytes in 1 blocks are still reachable in loss record 163 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52873362: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 1,025 bytes in 1 blocks are still reachable in loss record 164 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52B0D941: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5287323A: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 1,088 bytes in 1 blocks are still reachable in loss record 165 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52A52990: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528DEF73: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
}
==3059068== 1,280 bytes in 1 blocks are still reachable in loss record 166 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297CBD1: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x5297CC78: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873A99: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 1,536 bytes in 1 blocks are still reachable in loss record 167 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x13C12294: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13C5646D: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x13BE6F55: ??? (in /home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   obj:/home/<USER>/My_APP/TensorRT-8.6.1.6/targets/x86_64-linux-gnu/lib/libnvinfer_plugin.so.8.6.1
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 2,409 bytes in 2 blocks are still reachable in loss record 168 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x400DA02: calloc (rtld-malloc.h:44)
==3059068==    by 0x400DA02: _dl_new_object (dl-object.c:92)
==3059068==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==3059068==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==3059068==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400E34D: _dl_open (dl-open.c:883)
==3059068==    by 0x522B63B: dlopen_doit (dlopen.c:56)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x530FB62: _dl_catch_error (dl-error-skeleton.c:227)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   fun:calloc
   fun:_dl_new_object
   fun:_dl_map_object_from_fd
   fun:_dl_map_object
   fun:dl_open_worker_begin
   fun:_dl_catch_exception
   fun:dl_open_worker
   fun:_dl_catch_exception
   fun:_dl_open
   fun:dlopen_doit
   fun:_dl_catch_exception
   fun:_dl_catch_error
}
==3059068== 2,576 bytes in 1 blocks are still reachable in loss record 169 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x529E9A7D: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52873D79: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 3,888 bytes in 27 blocks are still reachable in loss record 170 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2781CCC3: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068==    by 0x29AC3A: std::__invoke_result<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >::type std::__invoke<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:96)
==3059068==    by 0x29A8E0: void std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::_M_invoke<0ul, 1ul>(std::_Index_tuple<0ul, 1ul>) (std_thread.h:259)
==3059068==    by 0x299F3D: std::thread::_Invoker<std::tuple<void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> > >::operator()() (std_thread.h:266)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZN2cv10ThreadPool12reconfigure_Ej
   fun:_ZN2cv10ThreadPool3runERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv13parallel_for_ERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt8__invokeIPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEENSt15__invoke_resultIT_JDpT0_EE4typeEOS7_DpOS8_
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEE9_M_invokeIJLm0ELm1EEEEvSt12_Index_tupleIJXspT_EEE
   fun:_ZNSt6thread8_InvokerISt5tupleIJPFvR15ResourceMonitorESt17reference_wrapperIS2_EEEEclEv
}
==3059068== 6,912 bytes in 27 blocks are still reachable in loss record 171 of 184
==3059068==    at 0x4849013: operator new(unsigned long) (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27863EFE: cv::TLSDataContainer::getData() const (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x27864B8E: cv::utils::getThreadID() (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781B48E: cv::WorkerThread::thread_loop_wrapper(void*) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x522FAC2: start_thread (pthread_create.c:442)
==3059068==    by 0x52C0A03: clone (clone.S:100)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_ZNK2cv16TLSDataContainer7getDataEv
   fun:_ZN2cv5utils11getThreadIDEv
   fun:_ZN2cv12WorkerThread19thread_loop_wrapperEPv
   fun:start_thread
   fun:clone
}
==3059068== 8,192 bytes in 1 blocks are still reachable in loss record 172 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x1627020E: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.*********)
==3059068==    by 0x1626AF5F: cufftCreate (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.*********)
==3059068==    by 0x1626BFE3: cufftPlanMany (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.*********)
==3059068==    by 0x4D1C9CA: scr5000::CuFFTPlanRAII::createMany(int, int*, int*, int, int, int*, int, int, cufftType_t, int) (resource_manager.hpp:210)
==3059068==    by 0x4D19FDA: FFTGPUOptimizer::initializeFFTPlan()::{lambda(scr5000::CuFFTPlanRAII*)#1}::operator()(scr5000::CuFFTPlanRAII*) const (fft_gpu.cpp:88)
==3059068==    by 0x4D1C797: bool std::__invoke_impl<bool, FFTGPUOptimizer::initializeFFTPlan()::{lambda(scr5000::CuFFTPlanRAII*)#1}&, scr5000::CuFFTPlanRAII*>(std::__invoke_other, FFTGPUOptimizer::initializeFFTPlan()::{lambda(scr5000::CuFFTPlanRAII*)#1}&, scr5000::CuFFTPlanRAII*&&) (invoke.h:61)
==3059068==    by 0x4D1C47F: std::enable_if<is_invocable_r_v<bool, FFTGPUOptimizer::initializeFFTPlan()::{lambda(scr5000::CuFFTPlanRAII*)#1}&, scr5000::CuFFTPlanRAII*>, bool>::type std::__invoke_r<bool, FFTGPUOptimizer::initializeFFTPlan()::{lambda(scr5000::CuFFTPlanRAII*)#1}&, scr5000::CuFFTPlanRAII*>(FFTGPUOptimizer::initializeFFTPlan()::{lambda(scr5000::CuFFTPlanRAII*)#1}&, scr5000::CuFFTPlanRAII*&&) (invoke.h:114)
==3059068==    by 0x4D1C2C7: std::_Function_handler<bool (scr5000::CuFFTPlanRAII*), FFTGPUOptimizer::initializeFFTPlan()::{lambda(scr5000::CuFFTPlanRAII*)#1}>::_M_invoke(std::_Any_data const&, scr5000::CuFFTPlanRAII*&&) (std_function.h:290)
==3059068==    by 0x4D73F64: std::function<bool (scr5000::CuFFTPlanRAII*)>::operator()(scr5000::CuFFTPlanRAII*) const (std_function.h:590)
==3059068==    by 0x4D72800: scr5000::GPUResourceManager::getOrCreateFFTPlan(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::function<bool (scr5000::CuFFTPlanRAII*)>) (resource_manager.cpp:267)
==3059068==    by 0x4D1A078: FFTGPUOptimizer::initializeFFTPlan() (fft_gpu.cpp:76)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.*********
   fun:cufftCreate
   fun:cufftPlanMany
   fun:_ZN7scr500013CuFFTPlanRAII10createManyEiPiS1_iiS1_ii11cufftType_ti
   fun:_ZZN15FFTGPUOptimizer17initializeFFTPlanEvENKUlPN7scr500013CuFFTPlanRAIIEE_clES2_
   fun:_ZSt13__invoke_implIbRZN15FFTGPUOptimizer17initializeFFTPlanEvEUlPN7scr500013CuFFTPlanRAIIEE_JS3_EET_St14__invoke_otherOT0_DpOT1_
   fun:_ZSt10__invoke_rIbRZN15FFTGPUOptimizer17initializeFFTPlanEvEUlPN7scr500013CuFFTPlanRAIIEE_JS3_EENSt9enable_ifIX16is_invocable_r_vIT_T0_DpT1_EES7_E4typeEOS8_DpOS9_
   fun:_ZNSt17_Function_handlerIFbPN7scr500013CuFFTPlanRAIIEEZN15FFTGPUOptimizer17initializeFFTPlanEvEUlS2_E_E9_M_invokeERKSt9_Any_dataOS2_
   fun:_ZNKSt8functionIFbPN7scr500013CuFFTPlanRAIIEEEclES2_
   fun:_ZN7scr500018GPUResourceManager18getOrCreateFFTPlanERKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEESt8functionIFbPNS_13CuFFTPlanRAIIEEE
   fun:_ZN15FFTGPUOptimizer17initializeFFTPlanEv
}
==3059068== 11,664 bytes in 27 blocks are possibly lost in loss record 173 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x40147D9: calloc (rtld-malloc.h:44)
==3059068==    by 0x40147D9: allocate_dtv (dl-tls.c:375)
==3059068==    by 0x40147D9: _dl_allocate_tls (dl-tls.c:634)
==3059068==    by 0x52307B4: allocate_stack (allocatestack.c:430)
==3059068==    by 0x52307B4: pthread_create@@GLIBC_2.34 (pthread_create.c:647)
==3059068==    by 0x2781C2A4: cv::WorkerThread::WorkerThread(cv::ThreadPool&, unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781CCD4: cv::ThreadPool::reconfigure_(unsigned int) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2781D6D4: cv::ThreadPool::run(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x278087C3: cv::parallel_for_(cv::Range const&, cv::ParallelLoopBody const&, double) (in /usr/local/lib/libopencv_core.so.4.8.1)
==3059068==    by 0x2721C2DD: cv::threshold(cv::_InputArray const&, cv::_OutputArray const&, double, double, int) (in /usr/local/lib/libopencv_imgproc.so.4.8.1)
==3059068==    by 0x4D61F7E: post_process_combined(float const*, int, int, float, float, int) (postprocess.cpp:130)
==3059068==    by 0x4D254C7: TargetDetection(char*, char*, InternalDetectionData*) (libSCR_5000_Alg.cpp:520)
==3059068==    by 0x226391: consumer(ResourceMonitor&) (test_algorithm.cpp:460)
==3059068==    by 0x29AEAF: void std::__invoke_impl<void, void (*)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor> >(std::__invoke_other, void (*&&)(ResourceMonitor&), std::reference_wrapper<ResourceMonitor>&&) (invoke.h:61)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: possible
   fun:calloc
   fun:calloc
   fun:allocate_dtv
   fun:_dl_allocate_tls
   fun:allocate_stack
   fun:pthread_create@@GLIBC_2.34
   fun:_ZN2cv12WorkerThreadC1ERNS_10ThreadPoolEj
   fun:_ZN2cv10ThreadPool12reconfigure_Ej
   fun:_ZN2cv10ThreadPool3runERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv13parallel_for_ERKNS_5RangeERKNS_16ParallelLoopBodyEd
   fun:_ZN2cv9thresholdERKNS_11_InputArrayERKNS_12_OutputArrayEddi
   fun:_Z21post_process_combinedPKfiiffi
   fun:_Z15TargetDetectionPcS_P21InternalDetectionData
   fun:_Z8consumerR15ResourceMonitor
   fun:_ZSt13__invoke_implIvPFvR15ResourceMonitorEJSt17reference_wrapperIS0_EEET_St14__invoke_otherOT0_DpOT1_
}
==3059068== 16,384 bytes in 1 blocks are still reachable in loss record 174 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52873094: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 16,688 bytes in 1 blocks are still reachable in loss record 175 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528E18C8: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 17,624 bytes in 1 blocks are still reachable in loss record 176 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528DEDDF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52A59818: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0DF64: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
}
==3059068== 49,152 bytes in 3 blocks are still reachable in loss record 177 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x528739AA: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== 72,704 bytes in 1 blocks are still reachable in loss record 178 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x54654D95: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x56920775: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x545DA8DA: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89)
==3059068==    by 0x6: ???
==3059068==    by 0x4006439: call_init.part.0 (dl-init.c:56)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x530FAF4: _dl_catch_exception (dl-error-skeleton.c:182)
==3059068==    by 0x400DFF5: dl_open_worker (dl-open.c:808)
==3059068==    by 0x400DFF5: dl_open_worker (dl-open.c:771)
==3059068==    by 0x530FA97: _dl_catch_exception (dl-error-skeleton.c:208)
==3059068==    by 0x400E34D: _dl_open (dl-open.c:883)
==3059068==    by 0x522B63B: dlopen_doit (dlopen.c:56)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libnvrtc.so.11.8.89
   obj:*
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   fun:_dl_catch_exception
   fun:dl_open_worker
   fun:dl_open_worker
   fun:_dl_catch_exception
   fun:_dl_open
   fun:dlopen_doit
}
==3059068== 72,704 bytes in 1 blocks are still reachable in loss record 179 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x2E1EB79F: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcublasLt.so.11.11.3.6)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libcublasLt.so.11.11.3.6
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 72,704 bytes in 1 blocks are still reachable in loss record 180 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x27C4E50F: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcublas.so.11.11.3.6)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libcublas.so.11.11.3.6
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 72,704 bytes in 1 blocks are still reachable in loss record 181 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x1621503F: ??? (in /usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.*********)
==3059068==    by 0x400647D: call_init.part.0 (dl-init.c:70)
==3059068==    by 0x4006567: call_init (dl-init.c:33)
==3059068==    by 0x4006567: _dl_init (dl-init.c:117)
==3059068==    by 0x40202C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/local/cuda-11.8/targets/x86_64-linux/lib/libcufft.so.*********
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
}
==3059068== 131,072 bytes in 1 blocks are still reachable in loss record 182 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x5297C6BF: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872A6E: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
}
==3059068== 168,960 bytes in 1 blocks are still reachable in loss record 183 of 184
==3059068==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52896FD9: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52B0E673: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x52872E0B: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
}
==3059068== 655,360 bytes in 1 blocks are still reachable in loss record 184 of 184
==3059068==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==3059068==    by 0x52873075: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x528EB277: ??? (in /usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01)
==3059068==    by 0x4DAB22A: __cudart525 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DAB327: __cudart1354 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x5234EE7: __pthread_once_slow (pthread_once.c:116)
==3059068==    by 0x4DFACF8: __cudart1635 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DA1C56: __cudart523 (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4DCD1D0: cudaStreamCreate (in /home/<USER>/My_Project/MSHNet_TensorRT_Test/build/libSCR_5000_AI.so)
==3059068==    by 0x4D72CAB: scr5000::CudaStreamRAII::CudaStreamRAII() (resource_manager.hpp:126)
==3059068==    by 0x4D73AF4: std::_MakeUniq<scr5000::CudaStreamRAII>::__single_object std::make_unique<scr5000::CudaStreamRAII>() (unique_ptr.h:962)
==3059068==    by 0x4D71B83: scr5000::GPUResourceManager::initialize() (resource_manager.cpp:122)
==3059068== 
{
   <insert_a_suppression_name_here>
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:calloc
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   obj:/usr/lib/x86_64-linux-gnu/libcuda.so.535.183.01
   fun:__cudart525
   fun:__cudart1354
   fun:__pthread_once_slow
   fun:__cudart1635
   fun:__cudart523
   fun:cudaStreamCreate
   fun:_ZN7scr500014CudaStreamRAIIC1Ev
   fun:_ZSt11make_uniqueIN7scr500014CudaStreamRAIIEJEENSt9_MakeUniqIT_E15__single_objectEDpOT0_
   fun:_ZN7scr500018GPUResourceManager10initializeEv
}
==3059068== LEAK SUMMARY:
==3059068==    definitely lost: 0 bytes in 0 blocks
==3059068==    indirectly lost: 0 bytes in 0 blocks
==3059068==      possibly lost: 14,216 bytes in 43 blocks
==3059068==    still reachable: 1,395,576 bytes in 302 blocks
==3059068==         suppressed: 0 bytes in 0 blocks
==3059068== 
==3059068== For lists of detected and suppressed errors, rerun with: -s
==3059068== ERROR SUMMARY: 17 errors from 17 contexts (suppressed: 0 from 0)
