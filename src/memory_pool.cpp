#include "memory_pool.hpp"
#include <cstdlib>
#include <cstring>

namespace scr5000 {

// ==================== 主机内存池实现 ====================
void* HostMemoryPool::allocate(size_t size) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 对齐大小
    size = alignSize(size);
    
    // 首先尝试找到合适的空闲块
    for (auto& block : blocks_) {
        if (block->is_free && block->size >= size) {
            block->is_free = false;
            block->last_used = std::chrono::steady_clock::now();
            total_used_ += block->size;
            
            spdlog::debug("Host pool: reused block {}B at {}", block->size, block->ptr);
            return block->ptr;
        }
    }
    
    // 如果没有合适的块，分配新块
    size_t block_size = std::max(size, DEFAULT_BLOCK_SIZE);
    
    // 检查池大小限制
    if (total_allocated_ + block_size > max_pool_size_) {
        spdlog::error("Host pool: out of memory (limit: {}MB)", max_pool_size_ / (1024*1024));
        return nullptr;
    }
    
    void* ptr = std::aligned_alloc(ALIGNMENT, block_size);
    if (!ptr) {
        spdlog::error("Host memory allocation failed (size: {}B)", block_size);
        return nullptr;
    }
    
    // 初始化内存
    std::memset(ptr, 0, block_size);
    
    auto block = std::make_unique<MemoryBlock>(ptr, block_size);
    block->is_free = false;
    block->last_used = std::chrono::steady_clock::now();
    
    ptr_to_index_[ptr] = blocks_.size();
    blocks_.push_back(std::move(block));
    
    total_allocated_ += block_size;
    total_used_ += block_size;
    
    spdlog::debug("Host pool: allocated new block {}B at {} (total: {}MB)", 
                 block_size, ptr, total_allocated_ / (1024*1024));
    
    return ptr;
}

void HostMemoryPool::deallocate(void* ptr) {
    if (!ptr) return;
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = ptr_to_index_.find(ptr);
    if (it == ptr_to_index_.end()) {
        spdlog::warn("Host pool: attempt to deallocate unknown pointer {}", ptr);
        return;
    }
    
    size_t index = it->second;
    if (index >= blocks_.size()) {
        spdlog::error("Host pool: invalid block index {}", index);
        return;
    }
    
    auto& block = blocks_[index];
    if (block->is_free) {
        spdlog::warn("Host pool: double free detected for pointer {}", ptr);
        return;
    }
    
    block->is_free = true;
    block->last_used = std::chrono::steady_clock::now();
    total_used_ -= block->size;
    
    spdlog::debug("Host pool: freed block {}B at {}", block->size, ptr);
}

void HostMemoryPool::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    spdlog::info("Host pool cleanup: freeing {} blocks ({}MB)", 
                blocks_.size(), total_allocated_ / (1024*1024));
    
    for (auto& block : blocks_) {
        if (block->ptr) {
            std::free(block->ptr);
        }
    }
    
    blocks_.clear();
    ptr_to_index_.clear();
    total_allocated_ = 0;
    total_used_ = 0;
}

std::string HostMemoryPool::getStatsString() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t free_blocks = 0;
    for (const auto& block : blocks_) {
        if (block->is_free) {
            free_blocks++;
        }
    }
    
    double utilization = total_allocated_ > 0 ? static_cast<double>(total_used_) / total_allocated_ : 0.0;
    
    return fmt::format(
        "Host Pool Stats: {:.1f}MB allocated, {:.1f}MB used ({:.1f}%), {} blocks ({} free)",
        total_allocated_ / (1024.0*1024.0),
        total_used_ / (1024.0*1024.0),
        utilization * 100.0,
        blocks_.size(),
        free_blocks
    );
}

// ==================== 内存池管理器实现 ====================
MemoryPoolManager::MemoryPoolManager() : initialized_(false) {}

MemoryPoolManager::~MemoryPoolManager() {
    cleanup();
}
bool MemoryPoolManager::initialize(size_t cuda_pool_size, size_t host_pool_size) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (initialized_.load()) {
            spdlog::warn("Memory pool manager already initialized");
            return true;
        }
        
        try {
            spdlog::info("Initializing memory pool manager...");
            
            // 创建CUDA内存池
            cuda_pool_ = std::make_unique<CudaMemoryPool>(cuda_pool_size);
            
            // 创建主机内存池
            host_pool_ = std::make_unique<HostMemoryPool>(host_pool_size);
            
            initialized_.store(true);
            spdlog::info("Memory pool manager initialized successfully");
            return true;
            
        } catch (const std::exception& e) {
            spdlog::error("Exception during memory pool manager initialization: {}", e.what());
            cleanup();
            return false;
        }
}

void MemoryPoolManager::cleanup() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_.load()) {
            return;
        }
        
        spdlog::info("Cleaning up memory pool manager...");
        
        cuda_pool_.reset();
        host_pool_.reset();
        
        initialized_.store(false);
        spdlog::info("Memory pool manager cleaned up");
}

bool MemoryPoolManager::isInitialized() const {
        return initialized_.load();
}

void* MemoryPoolManager::allocateCuda(size_t size) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_.load() || !cuda_pool_) {
            spdlog::error("Memory pool manager not initialized");
            return nullptr;
        }
        
        return cuda_pool_->allocate(size);
}

void* MemoryPoolManager::allocateHost(size_t size) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_.load() || !host_pool_) {
            spdlog::error("Memory pool manager not initialized");
            return nullptr;
        }
        
        return host_pool_->allocate(size);
}

void MemoryPoolManager::deallocateCuda(void* ptr) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (cuda_pool_) {
            cuda_pool_->deallocate(ptr);
        }
}

void MemoryPoolManager::deallocateHost(void* ptr) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (host_pool_) {
            host_pool_->deallocate(ptr);
        }
}

std::string MemoryPoolManager::getStatsString() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::string result = "Memory Pool Manager Status:\n";
        result += "  Initialized: " + std::string(initialized_.load() ? "Yes" : "No") + "\n";
        
        if (initialized_.load()) {
            if (cuda_pool_) {
                result += "  " + cuda_pool_->getStatsString() + "\n";
            }
            if (host_pool_) {
                result += "  " + host_pool_->getStatsString() + "\n";
            }
        }
        
        return result;
}

// ==================== 全局内存池管理器实例 ====================
static std::unique_ptr<MemoryPoolManager> g_memory_pool_manager = nullptr;
static std::mutex g_memory_pool_mutex;

MemoryPoolManager* getMemoryPoolManager() {
    std::lock_guard<std::mutex> lock(g_memory_pool_mutex);
    
    if (!g_memory_pool_manager) {
        g_memory_pool_manager = std::make_unique<MemoryPoolManager>();
    }
    
    return g_memory_pool_manager.get();
}

void releaseMemoryPoolManager() {
    std::lock_guard<std::mutex> lock(g_memory_pool_mutex);
    
    if (g_memory_pool_manager) {
        g_memory_pool_manager->cleanup();
        g_memory_pool_manager.reset();
    }
}

} // namespace scr5000
