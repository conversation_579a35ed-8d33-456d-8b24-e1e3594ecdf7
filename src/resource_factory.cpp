#include "resource_factory.hpp"
#include "resource_manager.hpp"
#include "memory_pool.hpp"
#include "fft_gpu.hpp"
#include <fstream>
#include <iostream>
#include <nlohmann/json.hpp>

namespace scr5000 {

// ==================== ResourceConfig 实现 ====================
ResourceConfig ResourceConfig::loadFromFile(const std::string& config_file) {
    ResourceConfig config;
    
    try {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            spdlog::warn("Config file not found: {}, using defaults", config_file);
            return config;
        }
        
        nlohmann::json j;
        file >> j;
        
        // 解析配置
        if (j.contains("gpu_memory_pool_size")) {
            config.gpu_memory_pool_size = j["gpu_memory_pool_size"];
        }
        if (j.contains("host_memory_pool_size")) {
            config.host_memory_pool_size = j["host_memory_pool_size"];
        }
        if (j.contains("tensorrt_engine_path")) {
            config.tensorrt_engine_path = j["tensorrt_engine_path"];
        }
        if (j.contains("enable_tensorrt_profiling")) {
            config.enable_tensorrt_profiling = j["enable_tensorrt_profiling"];
        }
        if (j.contains("fft_rows")) {
            config.fft_rows = j["fft_rows"];
        }
        if (j.contains("fft_cols")) {
            config.fft_cols = j["fft_cols"];
        }
        if (j.contains("log_level")) {
            config.log_level = j["log_level"];
        }
        if (j.contains("log_file_path")) {
            config.log_file_path = j["log_file_path"];
        }
        if (j.contains("enable_resource_monitoring")) {
            config.enable_resource_monitoring = j["enable_resource_monitoring"];
        }
        if (j.contains("monitoring_interval_seconds")) {
            config.monitoring_interval_seconds = j["monitoring_interval_seconds"];
        }
        
        spdlog::info("Loaded configuration from: {}", config_file);
        
    } catch (const std::exception& e) {
        spdlog::error("Failed to load config from {}: {}", config_file, e.what());
        spdlog::info("Using default configuration");
    }
    
    return config;
}

bool ResourceConfig::saveToFile(const std::string& config_file) const {
    try {
        nlohmann::json j;
        j["gpu_memory_pool_size"] = gpu_memory_pool_size;
        j["host_memory_pool_size"] = host_memory_pool_size;
        j["tensorrt_engine_path"] = tensorrt_engine_path;
        j["enable_tensorrt_profiling"] = enable_tensorrt_profiling;
        j["fft_rows"] = fft_rows;
        j["fft_cols"] = fft_cols;
        j["log_level"] = log_level;
        j["log_file_path"] = log_file_path;
        j["enable_resource_monitoring"] = enable_resource_monitoring;
        j["monitoring_interval_seconds"] = monitoring_interval_seconds;
        
        std::ofstream file(config_file);
        file << j.dump(4);
        
        spdlog::info("Saved configuration to: {}", config_file);
        return true;
        
    } catch (const std::exception& e) {
        spdlog::error("Failed to save config to {}: {}", config_file, e.what());
        return false;
    }
}

bool ResourceConfig::validate() const {
    if (gpu_memory_pool_size == 0) {
        spdlog::error("Invalid GPU memory pool size: {}", gpu_memory_pool_size);
        return false;
    }
    
    if (host_memory_pool_size == 0) {
        spdlog::error("Invalid host memory pool size: {}", host_memory_pool_size);
        return false;
    }
    
    if (fft_rows <= 0 || fft_cols <= 0) {
        spdlog::error("Invalid FFT dimensions: {}x{}", fft_rows, fft_cols);
        return false;
    }
    
    if (monitoring_interval_seconds <= 0) {
        spdlog::error("Invalid monitoring interval: {}", monitoring_interval_seconds);
        return false;
    }
    
    return true;
}

std::string ResourceConfig::getSummary() const {
    std::ostringstream oss;
    oss << "Resource Configuration Summary:\n";
    oss << "  GPU Memory Pool: " << gpu_memory_pool_size / (1024*1024) << " MB\n";
    oss << "  Host Memory Pool: " << host_memory_pool_size / (1024*1024) << " MB\n";
    oss << "  TensorRT Engine: " << (tensorrt_engine_path.empty() ? "Not specified" : tensorrt_engine_path) << "\n";
    oss << "  TensorRT Profiling: " << (enable_tensorrt_profiling ? "Enabled" : "Disabled") << "\n";
    oss << "  FFT Dimensions: " << fft_rows << "x" << fft_cols << "\n";
    oss << "  Log Level: " << log_level << "\n";
    oss << "  Log File: " << (log_file_path.empty() ? "Console only" : log_file_path) << "\n";
    oss << "  Resource Monitoring: " << (enable_resource_monitoring ? "Enabled" : "Disabled") << "\n";
    oss << "  Monitoring Interval: " << monitoring_interval_seconds << " seconds\n";
    return oss.str();
}

// ==================== ResourceLifecycleManager 实现 ====================
std::unique_ptr<ResourceLifecycleManager> ResourceLifecycleManager::instance_ = nullptr;
std::mutex ResourceLifecycleManager::instance_mutex_;

ResourceLifecycleManager* ResourceLifecycleManager::getInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    if (!instance_) {
        instance_ = std::unique_ptr<ResourceLifecycleManager>(new ResourceLifecycleManager());
    }
    return instance_.get();
}

void ResourceLifecycleManager::releaseInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    if (instance_) {
        instance_->cleanup();
        instance_.reset();
    }
}

bool ResourceLifecycleManager::initialize(const ResourceConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_.load()) {
        spdlog::warn("Resource lifecycle manager already initialized");
        return true;
    }
    
    try {
        spdlog::info("Initializing resource lifecycle manager...");
        
        // 验证配置
        if (!config.validate()) {
            spdlog::error("Invalid configuration");
            return false;
        }
        
        config_ = config;
        
        // 设置日志系统
        if (!ResourceFactory::setupLogging(config_)) {
            spdlog::error("Failed to setup logging");
            return false;
        }
        
        spdlog::info("Resource configuration:\n{}", config_.getSummary());
        
        initialized_.store(true);
        spdlog::info("Resource lifecycle manager initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception during resource lifecycle manager initialization: {}", e.what());
        cleanup();
        return false;
    }
}

void ResourceLifecycleManager::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_.load()) {
        return;
    }
    
    spdlog::info("Cleaning up resource lifecycle manager...");
    
    // 执行清理回调
    for (auto it = cleanup_callbacks_.rbegin(); it != cleanup_callbacks_.rend(); ++it) {
        try {
            (*it)();
        } catch (const std::exception& e) {
            spdlog::error("Exception during cleanup callback: {}", e.what());
        }
    }
    
    cleanup_callbacks_.clear();
    initialized_.store(false);
    
    spdlog::info("Resource lifecycle manager cleaned up");
}

void ResourceLifecycleManager::registerCleanupCallback(std::function<void()> callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    cleanup_callbacks_.push_back(std::move(callback));
}

std::string ResourceLifecycleManager::getStatusReport() const {
    // 注意：这里不能使用锁，因为方法是const的
    // std::lock_guard<std::mutex> lock(mutex_);
    
    std::ostringstream oss;
    oss << "Resource Lifecycle Manager Status:\n";
    oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\n";
    oss << "  Cleanup Callbacks: " << cleanup_callbacks_.size() << "\n";
    
    if (initialized_.load()) {
        oss << config_.getSummary();
    }
    
    return oss.str();
}

// ==================== ResourceFactory 实现 ====================
void* ResourceFactory::createGPUResourceManager(const ResourceConfig& config) {
    // 注意：这里返回nullptr，因为我们使用全局单例
    // 实际的GPU资源管理器通过getGPUResourceManager()获取
    spdlog::info("GPU resource manager creation requested (using global singleton)");
    return nullptr;
}

void* ResourceFactory::createMemoryPoolManager(const ResourceConfig& config) {
    // 注意：这里返回nullptr，因为我们使用全局单例
    // 实际的内存池管理器通过getMemoryPoolManager()获取
    spdlog::info("Memory pool manager creation requested (using global singleton)");
    return nullptr;
}

void* ResourceFactory::createFFTOptimizer(const ResourceConfig& config) {
    try {
        spdlog::info("Creating FFT GPU optimizer with dimensions: {}x{}",
                    config.fft_rows, config.fft_cols);
        // 注意：这里直接返回nullptr，因为FFTGPUOptimizer在其他地方创建
        // 实际应用中可以创建实例
        spdlog::info("FFT GPU optimizer creation requested (using existing instances)");
        return nullptr;
    } catch (const std::exception& e) {
        spdlog::error("Failed to create FFT GPU optimizer: {}", e.what());
        return nullptr;
    }
}

void* ResourceFactory::createResourceMonitor(const ResourceConfig& config) {
    // 这里我们返回nullptr，因为ResourceMonitor在测试程序中定义
    // 在实际应用中，可以创建一个通用的资源监控器
    spdlog::info("Resource monitor creation requested");
    return nullptr;
}

bool ResourceFactory::setupLogging(const ResourceConfig& config) {
    try {
        // 设置日志级别
        if (config.log_level == "trace") {
            spdlog::set_level(spdlog::level::trace);
        } else if (config.log_level == "debug") {
            spdlog::set_level(spdlog::level::debug);
        } else if (config.log_level == "info") {
            spdlog::set_level(spdlog::level::info);
        } else if (config.log_level == "warn") {
            spdlog::set_level(spdlog::level::warn);
        } else if (config.log_level == "error") {
            spdlog::set_level(spdlog::level::err);
        } else {
            spdlog::set_level(spdlog::level::info);
        }
        
        // 设置日志格式
        spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v");
        
        // 如果指定了日志文件，记录信息（简化版本）
        if (!config.log_file_path.empty()) {
            spdlog::info("Log file specified: {}", config.log_file_path);
            // 注意：这里简化了文件日志器的创建，避免依赖高级spdlog功能
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to setup logging: " << e.what() << std::endl;
        return false;
    }
}

// ==================== ResourceHealthChecker 实现 ====================
ResourceHealthChecker::ResourceHealthChecker(std::chrono::seconds interval)
    : running_(false), check_interval_(interval) {}

ResourceHealthChecker::~ResourceHealthChecker() {
    stop();
}

void ResourceHealthChecker::start() {
    if (running_.load()) {
        return;
    }

    running_.store(true);
    checker_thread_ = std::thread(&ResourceHealthChecker::healthCheckLoop, this);
    spdlog::info("Resource health checker started with interval: {}s", check_interval_.count());
}

void ResourceHealthChecker::stop() {
    if (!running_.load()) {
        return;
    }

    running_.store(false);
    if (checker_thread_.joinable()) {
        checker_thread_.join();
    }
    spdlog::info("Resource health checker stopped");
}

ResourceHealthChecker::HealthReport ResourceHealthChecker::performHealthCheck() {
    HealthReport report;

    try {
        // 检查GPU资源管理器
        auto* gpu_manager = getGPUResourceManager();
        if (gpu_manager && gpu_manager->isInitialized()) {
            report.gpu_manager_healthy = true;
        }

        // 检查内存池管理器
        auto* memory_manager = getMemoryPoolManager();
        if (memory_manager && memory_manager->isInitialized()) {
            report.memory_pool_healthy = true;
        }

        // 检查TensorRT引擎
        if (gpu_manager) {
            auto* tensorrt_engine = gpu_manager->getTensorRTEngine();
            if (tensorrt_engine && tensorrt_engine->is_valid()) {
                report.tensorrt_engine_healthy = true;
            }
        }

        // 获取内存使用情况
        // 这里可以添加更详细的内存使用统计

    } catch (const std::exception& e) {
        report.error_message = e.what();
        spdlog::error("Health check failed: {}", e.what());
    }

    return report;
}

void ResourceHealthChecker::healthCheckLoop() {
    while (running_.load()) {
        try {
            auto report = performHealthCheck();

            if (report.isHealthy()) {
                spdlog::debug("Health check passed");
            } else {
                spdlog::warn("Health check failed: GPU={}, Memory={}, TensorRT={}, Error='{}'",
                           report.gpu_manager_healthy, report.memory_pool_healthy,
                           report.tensorrt_engine_healthy, report.error_message);
            }

            // 等待下一次检查
            for (int i = 0; i < check_interval_.count() && running_.load(); ++i) {
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }

        } catch (const std::exception& e) {
            spdlog::error("Exception in health check loop: {}", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }
}

// ==================== 全局资源管理接口实现 ====================
namespace ResourceManager {
    static std::unique_ptr<ResourceHealthChecker> g_health_checker = nullptr;
    static std::mutex g_health_checker_mutex;

    bool initialize(const std::string& config_file) {
        auto config = ResourceConfig::loadFromFile(config_file);
        return initialize(config);
    }

    bool initialize(const ResourceConfig& config) {
        auto* manager = ResourceLifecycleManager::getInstance();
        return manager->initialize(config);
    }

    void cleanup() {
        stopHealthCheck();
        ResourceLifecycleManager::releaseInstance();
        releaseGPUResourceManager();
        releaseMemoryPoolManager();
    }

    bool isInitialized() {
        auto* manager = ResourceLifecycleManager::getInstance();
        return manager->isInitialized();
    }

    const ResourceConfig& getConfig() {
        auto* manager = ResourceLifecycleManager::getInstance();
        return manager->getConfig();
    }

    std::string getStatusReport() {
        auto* manager = ResourceLifecycleManager::getInstance();
        return manager->getStatusReport();
    }

    ResourceHealthChecker::HealthReport performHealthCheck() {
        std::lock_guard<std::mutex> lock(g_health_checker_mutex);

        if (!g_health_checker) {
            g_health_checker = std::make_unique<ResourceHealthChecker>();
        }

        return g_health_checker->performHealthCheck();
    }

    void startHealthCheck() {
        std::lock_guard<std::mutex> lock(g_health_checker_mutex);

        if (!g_health_checker) {
            g_health_checker = std::make_unique<ResourceHealthChecker>();
        }

        g_health_checker->start();
    }

    void stopHealthCheck() {
        std::lock_guard<std::mutex> lock(g_health_checker_mutex);

        if (g_health_checker) {
            g_health_checker->stop();
            g_health_checker.reset();
        }
    }
}

} // namespace scr5000
