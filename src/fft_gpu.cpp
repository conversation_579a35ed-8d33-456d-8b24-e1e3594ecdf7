#include "fft_gpu.hpp"
#include <stdexcept>
#include <cuda_runtime.h>
#include <cstring>
#include <unordered_map>
#include <spdlog/spdlog.h>
#include <sstream>

FFTGPUOptimizer::FFTGPUOptimizer(int rows, int cols)
    : ROWS(rows), COLS(cols), gpu_manager_(nullptr) {

    try {
        spdlog::info("Initializing FFT GPU Optimizer: {}x{}", rows, cols);

        // 获取GPU资源管理器
        gpu_manager_ = scr5000::getGPUResourceManager();
        if (!gpu_manager_ || !gpu_manager_->isInitialized()) {
            throw std::runtime_error("GPU resource manager not available or not initialized");
        }

        // 生成FFT计划名称
        fft_plan_name_ = fmt::format("fft_col_{}x{}", ROWS, COLS);

        // 初始化FFT计划
        if (!initializeFFTPlan()) {
            throw std::runtime_error("Failed to initialize FFT plan");
        }

        spdlog::info("FFT GPU Optimizer initialized successfully");

    } catch (const std::exception& e) {
        spdlog::error("FFT GPU Optimizer initialization failed: {}", e.what());
        cleanup();
        throw;
    }
}

FFTGPUOptimizer::FFTGPUOptimizer(int rows, int cols, scr5000::GPUResourceManager* gpu_manager)
    : ROWS(rows), COLS(cols), gpu_manager_(gpu_manager) {

    try {
        spdlog::info("Initializing FFT GPU Optimizer with provided GPU manager: {}x{}", rows, cols);

        if (!gpu_manager_ || !gpu_manager_->isInitialized()) {
            throw std::runtime_error("Provided GPU resource manager not available or not initialized");
        }

        // 生成FFT计划名称
        fft_plan_name_ = fmt::format("fft_col_{}x{}", ROWS, COLS);

        // 初始化FFT计划
        if (!initializeFFTPlan()) {
            throw std::runtime_error("Failed to initialize FFT plan");
        }

        spdlog::info("FFT GPU Optimizer initialized successfully with provided GPU manager");

    } catch (const std::exception& e) {
        spdlog::error("FFT GPU Optimizer initialization failed: {}", e.what());
        cleanup();
        throw;
    }
}

FFTGPUOptimizer::~FFTGPUOptimizer() {
    cleanup();
}

bool FFTGPUOptimizer::initializeFFTPlan() {
    if (!gpu_manager_) {
        spdlog::error("GPU resource manager not available");
        return false;
    }

    // 使用GPU资源管理器创建FFT计划
    auto* fft_plan = gpu_manager_->getOrCreateFFTPlan(fft_plan_name_,
        [this](scr5000::CuFFTPlanRAII* plan) -> bool {
            int rank = 1;
            int n[1] = { ROWS };
            int istride = COLS;
            int ostride = COLS;
            int idist = 1;
            int odist = 1;
            int inembed[1] = { ROWS };
            int onembed[1] = { ROWS };
            int batch = COLS;

            return plan->createMany(rank, n, inembed, istride, idist,
                                   onembed, ostride, odist, CUFFT_C2C, batch);
        });

    if (!fft_plan || !fft_plan->is_valid()) {
        spdlog::error("Failed to create FFT plan: {}", fft_plan_name_);
        return false;
    }

    spdlog::debug("FFT plan created successfully: {}", fft_plan_name_);
    return true;
}

void FFTGPUOptimizer::cleanup() {
    spdlog::debug("Cleaning up FFT GPU Optimizer resources");

    // 清理管理的缓冲区
    managed_buffers_.clear();

    // 注意：不直接清理GPU管理器，它有自己的生命周期
    gpu_manager_ = nullptr;

    spdlog::debug("FFT GPU Optimizer cleanup completed");
}

void FFTGPUOptimizer::performColumnwiseFFT_GPU(float* d_complexData) {
    if (!gpu_manager_) {
        throw std::runtime_error("GPU resource manager not available");
    }

    // 获取FFT计划
    auto* fft_plan = gpu_manager_->getOrCreateFFTPlan(fft_plan_name_, nullptr);
    if (!fft_plan || !fft_plan->is_valid()) {
        throw std::runtime_error("FFT plan not available: " + fft_plan_name_);
    }

    cufftComplex* d_data = reinterpret_cast<cufftComplex*>(d_complexData);
    cufftResult result = cufftExecC2C(fft_plan->get(), d_data, d_data, CUFFT_FORWARD);

    if (result != CUFFT_SUCCESS) {
        spdlog::error("cuFFT column-wise execution failed: {}", static_cast<int>(result));
        throw std::runtime_error("cuFFT column-wise execution failed");
    }

    spdlog::debug("Column-wise FFT executed successfully");
}

scr5000::CudaMemoryRAII<float>* FFTGPUOptimizer::allocateDeviceBuffer(size_t elements) {
    try {
        auto buffer = std::make_unique<scr5000::CudaMemoryRAII<float>>(elements);
        if (!buffer->is_valid()) {
            spdlog::error("Failed to allocate CUDA memory buffer: {} elements", elements);
            return nullptr;
        }

        scr5000::CudaMemoryRAII<float>* buffer_ptr = buffer.get();
        managed_buffers_.push_back(std::move(buffer));

        spdlog::debug("Allocated device buffer: {} elements ({}MB)",
                     elements, elements * sizeof(float) / (1024.0 * 1024.0));

        return buffer_ptr;

    } catch (const std::exception& e) {
        spdlog::error("Exception during device buffer allocation: {}", e.what());
        return nullptr;
    }
}

void FFTGPUOptimizer::deallocateDeviceBuffer(scr5000::CudaMemoryRAII<float>* buffer) {
    if (!buffer) {
        return;
    }

    // 从管理列表中移除
    auto it = std::find_if(managed_buffers_.begin(), managed_buffers_.end(),
        [buffer](const std::unique_ptr<scr5000::CudaMemoryRAII<float>>& ptr) {
            return ptr.get() == buffer;
        });

    if (it != managed_buffers_.end()) {
        spdlog::debug("Deallocating device buffer: {} elements", (*it)->count());
        managed_buffers_.erase(it);
    } else {
        spdlog::warn("Attempt to deallocate unknown buffer");
    }
}

std::string FFTGPUOptimizer::getResourceInfo() const {
    std::ostringstream oss;
    oss << "FFT GPU Optimizer Status:\n";
    oss << "  Dimensions: " << ROWS << "x" << COLS << "\n";
    oss << "  FFT Plan: " << fft_plan_name_ << "\n";
    oss << "  GPU Manager: " << (gpu_manager_ ? "Available" : "Not Available") << "\n";
    oss << "  Managed Buffers: " << managed_buffers_.size() << "\n";

    size_t total_memory = 0;
    for (const auto& buffer : managed_buffers_) {
        if (buffer && buffer->is_valid()) {
            total_memory += buffer->size_bytes();
        }
    }

    oss << "  Total Memory: " << total_memory / (1024.0 * 1024.0) << " MB\n";

    if (gpu_manager_) {
        auto* fft_plan = gpu_manager_->getOrCreateFFTPlan(fft_plan_name_, nullptr);
        oss << "  FFT Plan Status: " << (fft_plan && fft_plan->is_valid() ? "Valid" : "Invalid") << "\n";
    }

    return oss.str();
}

static ColumnSegment extractColumnSegment(
    const float* h_complexData,
    int col,
    int row)      // 原始行号
{
    int segment_start = 0;
    int segment_length = 0;

    // 根据 row 判断所在分段
    if (row >= 0 && row < 512) {
        segment_start = 0;
        segment_length = 512;
    } else if (row >= 512 && row < 768) {
        segment_start = 512;
        segment_length = 256;
    } else if (row >= 768 && row < 896) {
        segment_start = 768;
        segment_length = 128;
    } else if (row >= 896 && row < 960) {
        segment_start = 896;
        segment_length = 64;
    } else if (row >= 960 && row < 1024) {
        segment_start = 960;
        segment_length = 64;
    } else {
        throw std::runtime_error("Invalid y coordinate");
    }

    ColumnSegment seg;
    seg.segment_length = segment_length;
    seg.segment_start  = segment_start;
    seg.data.resize(segment_length);

    for (int r = 0; r < segment_length; ++r) {
        int src_row = segment_start + r;
        size_t src_idx = src_row * 2048 * 2 + col * 2;
        seg.data[r].x = h_complexData[src_idx];
        seg.data[r].y = h_complexData[src_idx + 1];
    }
    return seg;
}

// 单列FFT
std::vector<std::complex<float>> FFTGPUOptimizer::performColumnwiseFFTForCenters(
    const std::vector<std::pair<int, int>>& centers,
    const float* h_complexData)
{
    std::vector<std::complex<float>> results(centers.size());

    for (size_t i = 0; i < centers.size(); ++i) {
        int col = centers[i].first;
        int row = centers[i].second;

        // 1. 提取列数据段
        ColumnSegment seg = extractColumnSegment(h_complexData, col, row);

        // 2. 拷贝到设备
        cufftComplex* d_data = nullptr;
        if (cudaMalloc(&d_data, seg.segment_length * sizeof(cufftComplex)) != cudaSuccess)
            throw std::runtime_error("CUDA malloc failed");

        if (cudaMemcpy(d_data, seg.data.data(),
                       seg.segment_length * sizeof(cufftComplex),
                       cudaMemcpyHostToDevice) != cudaSuccess) {
            cudaFree(d_data);
            throw std::runtime_error("CUDA memcpy failed");
        }

        // 3. 创建并执行 FFT
        cufftHandle plan;
        if (cufftPlan1d(&plan, seg.segment_length, CUFFT_C2C, 1) != CUFFT_SUCCESS) {
            cudaFree(d_data);
            throw std::runtime_error("cuFFT plan failed");
        }

        cufftComplex* d_fft = nullptr;
        if (cudaMalloc(&d_fft, seg.segment_length * sizeof(cufftComplex)) != cudaSuccess) {
            cufftDestroy(plan);
            cudaFree(d_data);
            throw std::runtime_error("CUDA malloc failed");
        }

        if (cufftExecC2C(plan, d_data, d_fft, CUFFT_FORWARD) != CUFFT_SUCCESS) {
            cufftDestroy(plan);
            cudaFree(d_data);
            cudaFree(d_fft);
            throw std::runtime_error("cuFFT exec failed");
        }

        // 4. 取回结果并定位到 row 所在位置
        std::vector<cufftComplex> h_fft(seg.segment_length);
        if (cudaMemcpy(h_fft.data(), d_fft,
                       seg.segment_length * sizeof(cufftComplex),
                       cudaMemcpyDeviceToHost) != cudaSuccess) {
            cufftDestroy(plan);
            cudaFree(d_data);
            cudaFree(d_fft);
            throw std::runtime_error("CUDA memcpy failed");
        }

        int local_row = row - seg.segment_start;
        results[i] = std::complex<float>(h_fft[local_row].x, h_fft[local_row].y);

        // 5. 清理
        cufftDestroy(plan);
        cudaFree(d_data);
        cudaFree(d_fft);
    }

    return results;
}

// 对单个列数据段执行FFT - 使用RAII管理资源
std::complex<float> FFTGPUOptimizer::performFFTOnSegment(
    const std::vector<cufftComplex>& segment_data,
    int target_row,
    int segment_start)
{
    int segment_length = segment_data.size();

    if (segment_length <= 0) {
        throw std::runtime_error("Invalid segment length: " + std::to_string(segment_length));
    }

    // 检查CUDA上下文状态
    cudaError_t status = cudaGetLastError();
    if (status != cudaSuccess) {
        throw std::runtime_error("CUDA context error before FFT: " +
                                std::string(cudaGetErrorString(status)));
    }

    // RAII CUDA内存管理
    struct CudaMemoryRAII {
        void* ptr;
        explicit CudaMemoryRAII(size_t size) : ptr(nullptr) {
            cudaError_t status = cudaMalloc(&ptr, size);
            if (status != cudaSuccess) {
                throw std::runtime_error("CUDA malloc failed: " + std::string(cudaGetErrorString(status)));
            }
        }
        ~CudaMemoryRAII() {
            if (ptr) cudaFree(ptr);
        }
        void* get() const { return ptr; }
        // 禁止拷贝
        CudaMemoryRAII(const CudaMemoryRAII&) = delete;
        CudaMemoryRAII& operator=(const CudaMemoryRAII&) = delete;
    };

    // RAII cuFFT计划管理
    struct CufftPlanRAII {
        cufftHandle plan;
        explicit CufftPlanRAII(int n, cufftType type, int batch = 1) : plan(0) {
            cufftResult status = cufftPlan1d(&plan, n, type, batch);
            if (status != CUFFT_SUCCESS) {
                throw std::runtime_error("cuFFT plan creation failed");
            }
        }
        ~CufftPlanRAII() {
            if (plan) cufftDestroy(plan);
        }
        cufftHandle get() const { return plan; }
        // 禁止拷贝
        CufftPlanRAII(const CufftPlanRAII&) = delete;
        CufftPlanRAII& operator=(const CufftPlanRAII&) = delete;
    };

    try {
        // 使用RAII管理CUDA内存
        CudaMemoryRAII d_data(segment_length * sizeof(cufftComplex));
        CudaMemoryRAII d_fft(segment_length * sizeof(cufftComplex));

        // 使用RAII管理cuFFT计划
        CufftPlanRAII plan(segment_length, CUFFT_C2C, 1);

        // 拷贝数据到设备
        cudaError_t cuda_status = cudaMemcpy(
            d_data.get(),
            segment_data.data(),
            segment_length * sizeof(cufftComplex),
            cudaMemcpyHostToDevice
        );
        if (cuda_status != cudaSuccess) {
            throw std::runtime_error("CUDA memcpy to device failed: " + std::string(cudaGetErrorString(cuda_status)));
        }

        // 执行FFT
        cufftResult fft_status = cufftExecC2C(
            plan.get(),
            static_cast<cufftComplex*>(d_data.get()),
            static_cast<cufftComplex*>(d_fft.get()),
            CUFFT_FORWARD
        );
        if (fft_status != CUFFT_SUCCESS) {
            throw std::runtime_error("cuFFT execution failed");
        }

        // 取回结果
        std::vector<cufftComplex> h_fft(segment_length);
        cuda_status = cudaMemcpy(
            h_fft.data(),
            d_fft.get(),
            segment_length * sizeof(cufftComplex),
            cudaMemcpyDeviceToHost
        );
        if (cuda_status != cudaSuccess) {
            throw std::runtime_error("CUDA memcpy from device failed: " + std::string(cudaGetErrorString(cuda_status)));
        }

        // 定位到目标行位置
        int local_row = target_row - segment_start;
        if (local_row < 0 || local_row >= segment_length) {
            throw std::runtime_error("Target row out of segment bounds: " + std::to_string(local_row));
        }

        const cufftComplex& result = h_fft[local_row];
        return std::complex<float>(result.x, result.y);

    } catch (const std::exception& e) {
        // 所有RAII对象会自动清理资源
        throw std::runtime_error("FFT processing failed: " + std::string(e.what()));
    }
}