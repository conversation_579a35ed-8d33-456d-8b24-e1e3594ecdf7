#include "resource_manager.hpp"
#include "infer_engine.hpp"
#include <fstream>
#include <sstream>

namespace scr5000 {

// ==================== TensorRT引擎RAII实现 ====================
bool TensorRTEngineRAII::loadEngine(const std::string& engine_path) {
    cleanup();
    
    try {
        spdlog::info("Loading TensorRT engine from: {}", engine_path);
        
        // 检查文件是否存在
        std::ifstream file(engine_path, std::ios::binary);
        if (!file.good()) {
            spdlog::error("Engine file not found: {}", engine_path);
            return false;
        }
        
        // 读取引擎文件
        file.seekg(0, file.end);
        size_t size = file.tellg();
        file.seekg(0, file.beg);
        
        std::vector<char> engine_data(size);
        file.read(engine_data.data(), size);
        file.close();
        
        if (file.gcount() != static_cast<std::streamsize>(size)) {
            spdlog::error("Failed to read complete engine file: {}", engine_path);
            return false;
        }
        
        // 创建运行时
        nvinfer1::IRuntime* runtime_ptr = nvinfer1::createInferRuntime(gLogger);
        if (!runtime_ptr) {
            spdlog::error("Failed to create TensorRT runtime");
            return false;
        }
        runtime_.reset(runtime_ptr);
        
        // 反序列化引擎
        nvinfer1::ICudaEngine* engine_ptr = runtime_ptr->deserializeCudaEngine(
            engine_data.data(), size);
        if (!engine_ptr) {
            spdlog::error("Failed to deserialize TensorRT engine");
            runtime_.reset();
            return false;
        }
        engine_.reset(engine_ptr);
        
        // 创建执行上下文
        nvinfer1::IExecutionContext* context_ptr = engine_ptr->createExecutionContext();
        if (!context_ptr) {
            spdlog::error("Failed to create TensorRT execution context");
            engine_.reset();
            runtime_.reset();
            return false;
        }
        context_.reset(context_ptr);
        
        engine_path_ = engine_path;
        is_valid_ = true;
        
        spdlog::info("TensorRT engine loaded successfully: {}", engine_path);
        
        // 打印引擎信息
        spdlog::info("Engine info: {} bindings", engine_ptr->getNbBindings());
        for (int i = 0; i < engine_ptr->getNbBindings(); ++i) {
            auto dims = engine_ptr->getBindingDimensions(i);
            std::ostringstream oss;
            oss << "[";
            for (int j = 0; j < dims.nbDims; ++j) {
                if (j > 0) oss << "x";
                oss << dims.d[j];
            }
            oss << "]";
            spdlog::info("  Binding {}: {} {}", i, 
                        engine_ptr->getBindingName(i), oss.str());
        }
        
        return true;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception during TensorRT engine loading: {}", e.what());
        cleanup();
        return false;
    }
}

void TensorRTEngineRAII::cleanup() {
    if (is_valid_) {
        spdlog::debug("Cleaning up TensorRT resources");
        context_.reset();
        engine_.reset();
        runtime_.reset();
        engine_path_.clear();
        is_valid_ = false;
    }
}

// ==================== GPU资源管理器实现 ====================
GPUResourceManager::GPUResourceManager() : initialized_(false) {}

GPUResourceManager::~GPUResourceManager() {
    cleanup();
}
bool GPUResourceManager::initialize() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (initialized_.load()) {
            spdlog::warn("GPU resource manager already initialized");
            return true;
        }
        
        try {
            spdlog::info("Initializing GPU resource manager...");
            
            // 创建主CUDA流
            main_stream_ = std::make_unique<CudaStreamRAII>();
            if (!main_stream_->is_valid()) {
                spdlog::error("Failed to create main CUDA stream");
                return false;
            }
            
            // 创建TensorRT引擎管理器
            tensorrt_engine_ = std::make_unique<TensorRTEngineRAII>();
            
            initialized_.store(true);
            spdlog::info("GPU resource manager initialized successfully");
            return true;
            
        } catch (const std::exception& e) {
            spdlog::error("Exception during GPU resource manager initialization: {}", e.what());
            cleanup();
            return false;
        }
}

void GPUResourceManager::cleanup() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_.load()) {
            return;
        }
        
        spdlog::info("Cleaning up GPU resource manager...");
        
        // 清理FFT计划
        fft_plans_.clear();
        
        // 清理内存缓冲区
        input_buffer_.deallocate();
        output_buffer_.deallocate();
        
        // 清理TensorRT资源
        tensorrt_engine_.reset();
        
        // 清理CUDA流
        main_stream_.reset();
        
        initialized_.store(false);
        spdlog::info("GPU resource manager cleaned up");
}

bool GPUResourceManager::isInitialized() const {
        return initialized_.load();
}

std::string GPUResourceManager::getResourceInfo() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::ostringstream oss;
        oss << "GPU Resource Manager Status:\n";
        oss << "  Initialized: " << (initialized_.load() ? "Yes" : "No") << "\n";
        
        if (initialized_.load()) {
            oss << "  Main Stream: " << (main_stream_ && main_stream_->is_valid() ? "Valid" : "Invalid") << "\n";
            oss << "  TensorRT Engine: " << (tensorrt_engine_ && tensorrt_engine_->is_valid() ? "Loaded" : "Not Loaded") << "\n";
            oss << "  FFT Plans: " << fft_plans_.size() << "\n";
            oss << "  Input Buffer: " << (input_buffer_.is_valid() ? input_buffer_.size_bytes() : 0) << " bytes\n";
            oss << "  Output Buffer: " << (output_buffer_.is_valid() ? output_buffer_.size_bytes() : 0) << " bytes\n";
            
            // GPU内存信息
            size_t free_mem = 0, total_mem = 0;
            cudaError_t status = cudaMemGetInfo(&free_mem, &total_mem);
            if (status == cudaSuccess) {
                oss << "  GPU Memory: " << (total_mem - free_mem) / (1024*1024) << "MB used, ";
                oss << free_mem / (1024*1024) << "MB free, ";
                oss << total_mem / (1024*1024) << "MB total\n";
            }
        }
        
        return oss.str();
}

// 专用接口实现
bool GPUResourceManager::loadTensorRTEngine(const std::string& engine_path) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_.load()) {
            spdlog::error("GPU resource manager not initialized");
            return false;
        }
        
        return tensorrt_engine_->loadEngine(engine_path);
}

TensorRTEngineRAII* GPUResourceManager::getTensorRTEngine() {
        std::lock_guard<std::mutex> lock(mutex_);
        return tensorrt_engine_.get();
}

CudaStreamRAII* GPUResourceManager::getMainStream() {
        std::lock_guard<std::mutex> lock(mutex_);
        return main_stream_.get();
}

bool GPUResourceManager::allocateBuffers(size_t input_size, size_t output_size) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_.load()) {
            spdlog::error("GPU resource manager not initialized");
            return false;
        }
        
        // 分配输入缓冲区
        if (!input_buffer_.allocate(input_size)) {
            spdlog::error("Failed to allocate input buffer: {} elements", input_size);
            return false;
        }
        
        // 分配输出缓冲区
        if (!output_buffer_.allocate(output_size)) {
            spdlog::error("Failed to allocate output buffer: {} elements", output_size);
            input_buffer_.deallocate();
            return false;
        }
        
        spdlog::info("GPU buffers allocated: input={}MB, output={}MB",
                    input_buffer_.size_bytes() / (1024*1024),
                    output_buffer_.size_bytes() / (1024*1024));
        
        return true;
}

CudaMemoryRAII<float>* GPUResourceManager::getInputBuffer() {
    return &input_buffer_;
}

CudaMemoryRAII<float>* GPUResourceManager::getOutputBuffer() {
    return &output_buffer_;
}

CuFFTPlanRAII* GPUResourceManager::getOrCreateFFTPlan(const std::string& plan_name,
                                                     std::function<bool(CuFFTPlanRAII*)> creator) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = fft_plans_.find(plan_name);
        if (it != fft_plans_.end()) {
            return it->second.get();
        }
        
        auto plan = std::make_unique<CuFFTPlanRAII>();
        if (!creator(plan.get())) {
            spdlog::error("Failed to create FFT plan: {}", plan_name);
            return nullptr;
        }
        
        CuFFTPlanRAII* plan_ptr = plan.get();
        fft_plans_[plan_name] = std::move(plan);
        spdlog::debug("Created FFT plan: {}", plan_name);
        
        return plan_ptr;
}

// ==================== 全局GPU资源管理器实例 ====================
static std::unique_ptr<GPUResourceManager> g_gpu_manager = nullptr;
static std::mutex g_gpu_manager_mutex;

GPUResourceManager* getGPUResourceManager() {
    std::lock_guard<std::mutex> lock(g_gpu_manager_mutex);
    
    if (!g_gpu_manager) {
        g_gpu_manager = std::make_unique<GPUResourceManager>();
    }
    
    return g_gpu_manager.get();
}

void releaseGPUResourceManager() {
    std::lock_guard<std::mutex> lock(g_gpu_manager_mutex);
    
    if (g_gpu_manager) {
        g_gpu_manager->cleanup();
        g_gpu_manager.reset();
    }
}

} // namespace scr5000
