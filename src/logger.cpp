#include "logger.hpp"
// #include "config.hpp"
#include <iostream>

// #include <filesystem>

void initLogger(const std::string& log_path) {
    try {

        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        // 日志文件轮换: 文件名, 最大文件大小(5MB), 保留文件个数(3个)

        // 日志保存路径为json配置文件中的路径
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(log_path, 10 * 1024 * 1024, 3);

        console_sink->set_level(spdlog::level::warn);
        file_sink->set_level(spdlog::level::warn);

        auto logger = std::make_shared<spdlog::logger>("multi_sink",
                            spdlog::sinks_init_list{console_sink, file_sink});
        logger->set_level(spdlog::level::info);
        logger->set_pattern("[%Y-%m-%d %H:%M:%S] [%^%l%$] %v");

        spdlog::set_default_logger(logger);
        spdlog::flush_on(spdlog::level::info);

    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "日志初始化失败: " << ex.what() << std::endl;
    }
}