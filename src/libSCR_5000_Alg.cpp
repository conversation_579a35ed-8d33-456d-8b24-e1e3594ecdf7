#include "libSCR_5000_Alg.hpp"
#include "preprocess.hpp"
#include "postprocess.hpp"
#include "infer_engine.hpp"
#include "PointTracker.hpp"
#include "logger.hpp"
#include "config.hpp"
#include "fft_gpu.hpp"
#include "resource_manager.hpp"
#include "memory_pool.hpp"

#include "TutorialConfig.h"

#include <cuda_runtime_api.h>
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <memory>
#include <ctime>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <stdexcept>

// ==================== 线程安全的全局资源管理 ====================
// 使用新的资源管理器替代原有的全局资源管理
using namespace scr5000;

// 线程安全的初始化状态管理
static std::mutex g_init_mutex;
static std::atomic<bool> g_initialized{false};

// 算法库专用资源管理器
class AlgorithmResourceManager {
private:
    GPUResourceManager* gpu_manager_;
    MemoryPoolManager* memory_manager_;
    std::vector<float> output_prob_;
    std::vector<float> sample_input_;
    std::atomic<bool> initialized_;
    mutable std::mutex mutex_;

public:
    AlgorithmResourceManager()
        : gpu_manager_(nullptr), memory_manager_(nullptr), initialized_(false) {}

    ~AlgorithmResourceManager() {
        cleanup();
    }

    bool initialize() {
        std::lock_guard<std::mutex> lock(mutex_);

        if (initialized_.load()) {
            return true;
        }

        try {
            // 获取GPU资源管理器
            gpu_manager_ = getGPUResourceManager();
            if (!gpu_manager_->initialize()) {
                spdlog::error("Failed to initialize GPU resource manager");
                return false;
            }

            // 获取内存池管理器
            memory_manager_ = getMemoryPoolManager();
            if (!memory_manager_->initialize()) {
                spdlog::error("Failed to initialize memory pool manager");
                return false;
            }

            // 预分配CPU内存
            output_prob_.reserve(512 * 1024); // 预留512K元素
            sample_input_.reserve(1024 * 1024 * 2); // 预留2M元素

            initialized_.store(true);
            spdlog::info("Algorithm resource manager initialized successfully");
            return true;

        } catch (const std::exception& e) {
            spdlog::error("Exception during algorithm resource manager initialization: {}", e.what());
            cleanup();
            return false;
        }
    }

    void cleanup() {
        std::lock_guard<std::mutex> lock(mutex_);

        if (!initialized_.load()) {
            return;
        }

        spdlog::info("Cleaning up algorithm resource manager...");

        // 清理CPU内存
        output_prob_.clear();
        output_prob_.shrink_to_fit();
        sample_input_.clear();
        sample_input_.shrink_to_fit();

        // 注意：不直接清理全局管理器，它们有自己的生命周期
        gpu_manager_ = nullptr;
        memory_manager_ = nullptr;

        initialized_.store(false);
        spdlog::info("Algorithm resource manager cleaned up");
    }

    bool isInitialized() const {
        return initialized_.load();
    }

    GPUResourceManager* getGPUManager() const {
        return gpu_manager_;
    }

    MemoryPoolManager* getMemoryManager() const {
        return memory_manager_;
    }

    std::vector<float>& getOutputProb() {
        return output_prob_;
    }

    std::vector<float>& getSampleInput() {
        return sample_input_;
    }
};

static std::unique_ptr<AlgorithmResourceManager> g_algorithm_resources = nullptr;

// 跟踪器资源
static std::unique_ptr<PointTracker> g_tracker = nullptr;
static std::vector<Point> g_current_group_detections;
static int g_group_start_frame = -1;
static const int FRAMES_PER_GROUP = 120;
static float g_prev_azimuth = -999.0f;
static bool g_azimuth_unchanged = false;

static std::unique_ptr<FFTGPUOptimizer> g_fft = nullptr;
static std::unique_ptr<ConfigManager> g_config_manager = nullptr;

static std::atomic<bool> s_lib_initialized{false};
static std::atomic<bool> s_init_failed{false};
static std::mutex s_init_mutex;
// 内存监控类
class MemoryMonitor {
public:
    static void logMemoryUsage(const std::string& checkpoint) {
        try {
            size_t free_mem = 0, total_mem = 0;
            cudaError_t status = cudaMemGetInfo(&free_mem, &total_mem);
            if (status == cudaSuccess) {
                size_t used_mem = total_mem - free_mem;
                spdlog::info("GPU Memory at {}: Used {:.1f}MB, Free {:.1f}MB, Total {:.1f}MB",
                            checkpoint,
                            used_mem / (1024.0 * 1024.0),
                            free_mem / (1024.0 * 1024.0),
                            total_mem / (1024.0 * 1024.0));
            } else {
                spdlog::warn("Failed to get GPU memory info at {}: {}", checkpoint, cudaGetErrorString(status));
            }
        } catch (const std::exception& e) {
            spdlog::warn("Exception in memory monitoring at {}: {}", checkpoint, e.what());
        }
    }

    static void logCPUMemoryUsage(const std::string& checkpoint) {
        try {
            std::ifstream status_file("/proc/self/status");
            std::string line;
            while (std::getline(status_file, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label;
                    long kb = 0;
                    iss >> label >> kb;          // 直接读成整数
                    double mb = kb / 1024.0;     // KB→MB
                    spdlog::warn("CPU Memory at {}: RSS {:.2f} MB", checkpoint, mb);
                    break;
                }
            }
        } catch (const std::exception& e) {
            spdlog::warn("Exception in CPU memory monitoring at {}: {}", checkpoint, e.what());
        }
    }
};

// using BatchCb = std::function<void(const TrackingResult*, int /*batch_size*/)>;

// ==================== 内部辅助函数 ====================
ALGORITHM_API void GetVersionInfo(AlgorithmVersion* version_info) {
    static const AlgorithmVersion version = {
        ALGO_VERSION_MAJOR,
        ALGO_VERSION_MINOR,
        ALGO_VERSION_PATCH,
        ALGO_VERSION_STRING,
        ALGO_BUILD_TIMESTAMP
    };
    if (version_info) {
        *version_info = version;
    }
}

// 清理算法资源的内部函数
static void CleanupAlgorithmResources() {
    std::lock_guard<std::mutex> lock(g_init_mutex);
    if (g_algorithm_resources) {
        g_algorithm_resources->cleanup();
        g_algorithm_resources.reset();
    }
    g_initialized.store(false);
}

// 初始化算法资源
static int InitializeAlgorithmResources(const std::string& engine_path) {
    std::lock_guard<std::mutex> lock(g_init_mutex);

    if (g_initialized.load()) {
        spdlog::debug("Algorithm resources already initialized");
        return 0;
    }

    try {
        spdlog::info("初始化算法资源...");

        // 创建算法资源管理器
        g_algorithm_resources = std::make_unique<AlgorithmResourceManager>();
        if (!g_algorithm_resources->initialize()) {
            spdlog::error("Failed to initialize algorithm resource manager");
            return -1;
        }

        // 初始化自定义插件
        initializeCustomPlugins();

        // 获取GPU资源管理器并加载TensorRT引擎
        auto* gpu_manager = g_algorithm_resources->getGPUManager();
        if (!gpu_manager->loadTensorRTEngine(engine_path)) {
            spdlog::error("Failed to load TensorRT engine from: {}", engine_path);
            CleanupAlgorithmResources();
            return -1;
        }

        // 获取引擎信息并分配缓冲区
        TensorRTEngineRAII* tensorrt_engine = gpu_manager->getTensorRTEngine();
        if (!tensorrt_engine || !tensorrt_engine->is_valid()) {
            spdlog::error("TensorRT engine is not valid");
            CleanupAlgorithmResources();
            return -1;
        }

        nvinfer1::ICudaEngine* engine = tensorrt_engine->getEngine();
        const auto input_dims = engine->getBindingDimensions(0);
        const auto output_dims = engine->getBindingDimensions(1);

        const size_t input_size = input_dims.d[4] * input_dims.d[2] * input_dims.d[3];
        const size_t output_size = output_dims.d[2] * output_dims.d[3];

        spdlog::info("模型输入维度: [{}x{}x{}x{}]", input_dims.d[1], input_dims.d[2], input_dims.d[3], input_dims.d[4]);

        // 分配GPU缓冲区
        if (!gpu_manager->allocateBuffers(input_size, output_size)) {
            spdlog::error("Failed to allocate GPU buffers");
            CleanupAlgorithmResources();
            return -1;
        }

        // 预分配CPU内存
        auto& output_prob = g_algorithm_resources->getOutputProb();
        auto& sample_input = g_algorithm_resources->getSampleInput();

        try {
            output_prob.resize(output_size);
            sample_input.resize(1024 * 1024 * 2);
        } catch (const std::bad_alloc& e) {
            spdlog::error("Failed to allocate CPU memory: {}", e.what());
            CleanupAlgorithmResources();
            return -1;
        }

        g_initialized.store(true);
        spdlog::info("算法资源初始化成功");

        // 记录初始化后的内存使用情况
        MemoryMonitor::logMemoryUsage("Algorithm resources initialization complete");
        MemoryMonitor::logCPUMemoryUsage("Algorithm resources initialization complete");

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception during algorithm resources initialization: {}", e.what());
        CleanupAlgorithmResources();
        return -1;
    }
}

// ==================== 公共API函数 ====================

// 算法库初始化
ALGORITHM_API int InitializeAlgorithmLibrary(const char* config_path) {

    if (s_lib_initialized.load(std::memory_order_acquire)) {
        spdlog::warn("InitializeAlgorithmLibrary has already been called, skipping re-initialization");
        return 0;  // 已经初始化过了
    }

    if (s_init_failed.load(std::memory_order_acquire)) {
        spdlog::error("Previous initialization failed, cannot re-initialize");
        return -1;
    }

    std::lock_guard<std::mutex> lock(s_init_mutex);
    if (s_lib_initialized.load(std::memory_order_relaxed)) {
        spdlog::warn("InitializeAlgorithmLibrary has already been called, skipping re-initialization");
        return 0;
    }

    if (s_init_failed.load(std::memory_order_relaxed)) {
        spdlog::error("Previous initialization failed, cannot re-initialize");
        return -1;
    }

    if (!config_path) {
        spdlog::error("Invalid config path");
        return -1;
    }

    try {
        // 加载配置
        g_config_manager = std::make_unique<ConfigManager>(config_path);

        // 日志路径
        std::string log_path = *g_config_manager->get<std::string>("/io_settings/logging/log_file_path");

        // 初始化日志
        initLogger(log_path);
        spdlog::info("=====算法库初始化开始...=====");

        // 初始化算法资源（如果尚未初始化）
        if (!g_initialized.load()) {
            std::string engine_path = *g_config_manager->get<std::string>("/io_settings/engine_paths");

            int init_result = -1;
            std::ifstream test_file(engine_path);
            if (test_file.good()) {
                init_result = InitializeAlgorithmResources(engine_path);
            }

            if (init_result != 0) {
                spdlog::error("Failed to initialize algorithm resources");
                return -2;
            }
        }

        // 加载查表数据
        std::string table_path = *g_config_manager->get<std::string>("/io_settings/table_paths");
        bool table_loaded = false;
        std::ifstream table_file(table_path);
        if (table_file.good()) {
            spdlog::info("加载俯仰角查表数据: {}", table_path);
            loadHechaTable_(table_path);
            table_loaded = true;
        }

        if (!table_loaded) {
            spdlog::warn("Could not find hecha_table.csv, using default values");
        }

        // 初始化跟踪器
        g_tracker = std::make_unique<PointTracker>(
            *g_config_manager->get<int>("/algorithm_settings/tracker/max_age"),
            *g_config_manager->get<int>("/algorithm_settings/tracker/reid_age"),
            *g_config_manager->get<float>("/algorithm_settings/tracker/distance_threshold"),
            *g_config_manager->get<int>("/algorithm_settings/tracker/min_hits")
        );
        spdlog::info("跟踪器初始化成功: max_age={}, reid_age={}, distance_threshold={}, min_hits={}",
            *g_config_manager->get<int>("/algorithm_settings/tracker/max_age"),
            *g_config_manager->get<int>("/algorithm_settings/tracker/reid_age"),
            *g_config_manager->get<float>("/algorithm_settings/tracker/distance_threshold"),
            *g_config_manager->get<int>("/algorithm_settings/tracker/min_hits")
        );

        // 初始化GPU FFT优化器
        g_fft = std::make_unique<FFTGPUOptimizer>(1024, 2048);

        // 标记为已初始化
        s_lib_initialized.store(true, std::memory_order_release);
        spdlog::info("=====算法库初始化完成=====");

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in InitializeAlgorithmLibrary: {}", e.what());
        s_init_failed.store(true, std::memory_order_release);
        return -3;
    }
}

// ==================== 目标检测算法实现 ====================
ALGORITHM_API int TargetDetection(
    char* input_head_data,
    char* input_data,
    InternalDetectionData* detection_data
) {
    spdlog::info("=====开始执行轻量化目标检测...=====");

    // 验证目标检测前资源使用
    MemoryMonitor::logCPUMemoryUsage("Target detection start");

    // 参数验证
    if (!input_data || !detection_data) {
        spdlog::error("Invalid input parameters for TargetDetection");
        return -1;
    }

    // size_t frame_idx = 1;
    auto frame_view = getFrameDataView(input_head_data, input_data, *g_config_manager->get<int>("/algorithm_settings/detection/frame_idx"));
    const FrameHeader_Alg* S_head_ptr = frame_view.head_S;
    const float* S_data = frame_view.data_S;
    // const FrameHeader_Alg* D_head = frame_view.head_D;
    const float* D_data = frame_view.data_D;

    // 初始化输出参数
    detection_data->detection_results = nullptr;
    detection_data->num_detections = 0;
    detection_data->S_head = S_head_ptr;
    detection_data->column_segments = nullptr;
    detection_data->num_segments = 0;

    try {

        // 检查算法资源是否已初始化
        if (!g_algorithm_resources || !g_algorithm_resources->isInitialized()) {
            spdlog::error("Algorithm resources not initialized");
            return -1;
        }

        // 获取资源管理器
        auto* gpu_manager = g_algorithm_resources->getGPUManager();
        auto& sample_input = g_algorithm_resources->getSampleInput();
        auto& output_prob = g_algorithm_resources->getOutputProb();

        // 预处理：采样、归一化和FFT处理
        auto t0 = std::chrono::high_resolution_clock::now();
        sample_and_normalize(S_data, 1024 * 2048 * 2, sample_input);
        auto t1 = std::chrono::high_resolution_clock::now();

        // 获取GPU缓冲区和流
        CudaMemoryRAII<float>* input_buffer = gpu_manager->getInputBuffer();
        CudaMemoryRAII<float>* output_buffer = gpu_manager->getOutputBuffer();
        CudaStreamRAII* stream = gpu_manager->getMainStream();
        TensorRTEngineRAII* tensorrt_engine = gpu_manager->getTensorRTEngine();

        if (!input_buffer->is_valid() || !output_buffer->is_valid() ||
            !stream->is_valid() || !tensorrt_engine->is_valid()) {
            spdlog::error("GPU resources not properly initialized");
            return -1;
        }

        // 上传数据到GPU
        cudaError_t cuda_status = cudaMemcpyAsync(
            input_buffer->get(),
            sample_input.data(),
            sample_input.size() * sizeof(float),
            cudaMemcpyHostToDevice,
            stream->get()
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy input data to GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        // 执行推理
        void* buffers[2] = {input_buffer->get(), output_buffer->get()};
        bool inference_result = tensorrt_engine->getContext()->enqueueV2(buffers, stream->get(), nullptr);
        if (!inference_result) {
            spdlog::error("TensorRT inference failed");
            return -1;
        }

        // 下载结果
        cuda_status = cudaMemcpyAsync(
            output_prob.data(),
            output_buffer->get(),
            output_prob.size() * sizeof(float),
            cudaMemcpyDeviceToHost,
            stream->get()
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy output data from GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        stream->synchronize();
        auto t2 = std::chrono::high_resolution_clock::now();

        // 基本后处理：二值化和连通组件分析
        auto centers = post_process_combined(output_prob.data(), 512, 1024,
            *g_config_manager->get<int>("/algorithm_settings/detection/threshold"),
            *g_config_manager->get<float>("/algorithm_settings/detection/max_ratio"),
            *g_config_manager->get<int>("/algorithm_settings/detection/min_area"));

        if (centers.empty()) {
            spdlog::warn("无效帧:{} 模型没有检测到目标，跳过处理", S_head_ptr[0].frame_num);
            detection_data->detection_results = nullptr;
            detection_data->num_detections = 0;
            return 0;
        }

        if (centers[0].first == -1) {
            spdlog::warn("异常帧:{} 目标过多，跳过处理", S_head_ptr[0].frame_num);
            detection_data->detection_results = nullptr;
            detection_data->num_detections = 0;
            return 0;
        }

        auto t3 = std::chrono::high_resolution_clock::now();

        // 分配输出内存
        detection_data->num_detections = centers.size();
        if (detection_data->num_detections > 0) {
            try {
                detection_data->detection_results = new DetectionResult[detection_data->num_detections];
            } catch (const std::bad_alloc& e) {
                spdlog::error("Failed to allocate memory for detection results: {}", e.what());
                detection_data->num_detections = 0;
                return -1;
            }
        } else {
            detection_data->detection_results = nullptr;
        }

        spdlog::info("检测到 {} 个目标中心点", detection_data->num_detections);

        // 填充轻量化检测结果 - 仅包含基本信息
        for (size_t i = 0; i < centers.size(); ++i) {
            detection_data->detection_results[i].row = centers[i].second;  // y坐标对应行
            detection_data->detection_results[i].col = centers[i].first;   // x坐标对应列
            detection_data->detection_results[i].frame = S_head_ptr[0].frame_num; // 从帧头获取帧号

            spdlog::info("检测点[{}]: 行={}, 列={}, 帧={}", i,
                         detection_data->detection_results[i].row, detection_data->detection_results[i].col, detection_data->detection_results[i].frame);
        }

        // 提取列数据段供跟踪函数使用
        spdlog::debug("开始提取列数据段，共 {} 个中心点", centers.size());
        detection_data->num_segments = centers.size();
        try {
            // std::this_thread::sleep_for(std::chrono::milliseconds(100));
            detection_data->column_segments = new ColumnSegmentData[detection_data->num_segments];
        } catch (const std::bad_alloc& e) {
            spdlog::error("Failed to allocate memory for column segments: {}", e.what());
            // 清理已分配的检测结果内存
            if (detection_data->detection_results) {
                delete[] detection_data->detection_results;
                detection_data->detection_results = nullptr;
            }
            detection_data->num_detections = 0;
            detection_data->num_segments = 0;
            return -1;
        }

        for (size_t i = 0; i < centers.size(); ++i) {
            int col = centers[i].first;
            int row = centers[i].second;
            try {
                detection_data->column_segments[i] = extractColumnSegmentData(S_data, D_data, col, row);
                spdlog::debug("提取列数据段[{}]: 列={}, 行={}, 段长度={}", i, col, row, detection_data->column_segments[i].segment_length);
            } catch (const std::exception& e) {
                spdlog::error("Failed to extract column segment for center {}: {}", i, e.what());
                // 清理已分配的内存
                for (size_t j = 0; j < i; ++j) {
                    delete[] detection_data->column_segments[j].data_S;
                    delete[] detection_data->column_segments[j].data_D;
                }
                delete[] detection_data->column_segments;
                detection_data->column_segments = nullptr;
                if (detection_data->detection_results) {
                    delete[] detection_data->detection_results;
                    detection_data->detection_results = nullptr;
                }
                detection_data->num_detections = 0;
                detection_data->num_segments = 0;
                return -1;
            }
        }

        auto t4 = std::chrono::high_resolution_clock::now();

        // 记录性能信息
        int t_pre = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        int t_inf = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
        int t_post = std::chrono::duration_cast<std::chrono::milliseconds>(t3 - t2).count();
        int t_extract = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t3).count();
        int total = t_pre + t_inf + t_post + t_extract;

        spdlog::info("目标检测耗时(ms): 预处理:{} 推理:{} 后处理:{} 提取:{} 总:{} (FPS:{:.2f})",
                     t_pre, t_inf, t_post, t_extract, total, 1000.0 / total);

        // 记录目标检测后的内存使用情况
        MemoryMonitor::logMemoryUsage("Target detection complete");
        MemoryMonitor::logCPUMemoryUsage("Target detection complete");
        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetDetection: {}", e.what());
        // 清理已分配的内存
        if (detection_data->detection_results) {
            delete[] detection_data->detection_results;
            detection_data->detection_results = nullptr;
        }
        detection_data->num_detections = 0;
        return -1;
    }
}

// ==================== 目标跟踪算法实现 ====================
ALGORITHM_API void TargetTracking(
    const InternalDetectionData* detection_data,
    TrackingResult* tracking_results,
    int* num_tracks,
    int max_tracks,
    BatchCb batch_cb
) {
    spdlog::info("=====开始目标跟踪（包含FFT和俯仰角计算）...======");

    // MemoryMonitor::logMemoryUsage("Before TargetTracking");
    MemoryMonitor::logCPUMemoryUsage("Before TargetTracking");

    if (!detection_data || detection_data->num_detections <= 0) {
        spdlog::warn("没有检测到目标，跳过跟踪");
        ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
        *num_tracks = 0;
        return;
    }

    // 参数验证
    if (!tracking_results || !num_tracks || max_tracks <= 0 || max_tracks > MAX_TRACKING_RESULTS) {
        spdlog::error("Invalid input parameters for TargetTracking");
        ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
        *num_tracks = 0;
        return;
    }

    // 验证检测数据的完整性
    if (!detection_data->detection_results || !detection_data->S_head || !detection_data->column_segments ||
        detection_data->num_segments != detection_data->num_detections) {
        spdlog::error("Invalid detection data for TargetTracking");
        ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
        *num_tracks = 0;
        return;
    }

    try {
        // 执行FFT处理和俯仰角计算
        auto t_fft_start = std::chrono::high_resolution_clock::now();

        // 将检测结果转换为中心点格式
        std::vector<std::pair<int, int>> centers;
        centers.reserve(detection_data->num_detections);
        for (int i = 0; i < detection_data->num_detections; ++i) {
            centers.emplace_back(detection_data->detection_results[i].col, detection_data->detection_results[i].row);
            spdlog::debug("处理检测点[{}]: 列={}, 行={}", i, detection_data->detection_results[i].col, detection_data->detection_results[i].row);
        }

        // 使用传入的列数据段进行FFT处理
        spdlog::debug("开始FFT处理，处理 {} 个列数据段", detection_data->num_segments);
        std::vector<std::complex<float>> S_complexData;
        std::vector<std::complex<float>> D_complexData;
        S_complexData.reserve(detection_data->num_segments);
        D_complexData.reserve(detection_data->num_segments);

        for (int i = 0; i < detection_data->num_segments; ++i) {
            const auto& segment = detection_data->column_segments[i];

            // 对S通道和D通道数据分别执行FFT
            try {
                // 将S通道数据转换为cufftComplex格式
                std::vector<cufftComplex> S_segment_data(segment.segment_length);
                for (int j = 0; j < segment.segment_length; ++j) {
                    S_segment_data[j].x = segment.data_S[j * 2];     // 实部
                    S_segment_data[j].y = segment.data_S[j * 2 + 1]; // 虚部
                }

                // 将D通道数据转换为cufftComplex格式
                std::vector<cufftComplex> D_segment_data(segment.segment_length);
                for (int j = 0; j < segment.segment_length; ++j) {
                    D_segment_data[j].x = segment.data_D[j * 2];     // 实部
                    D_segment_data[j].y = segment.data_D[j * 2 + 1]; // 虚部
                }

                // 执行FFT处理
                auto S_result = g_fft->performFFTOnSegment(S_segment_data, segment.row, segment.segment_start);
                auto D_result = g_fft->performFFTOnSegment(D_segment_data, segment.row, segment.segment_start);

                S_complexData.push_back(S_result);
                D_complexData.push_back(D_result);

                spdlog::debug("FFT处理完成[{}]: 列={}, 行={}, 段长度={}", i, segment.col, segment.row, segment.segment_length);
            } catch (const std::exception& e) {
                spdlog::error("FFT处理失败[{}]: {}", i, e.what());
                // 使用原始数据作为备用
                int local_row = segment.row - segment.segment_start;
                if (local_row >= 0 && local_row < segment.segment_length) {
                    S_complexData.emplace_back(segment.data_S[local_row * 2], segment.data_S[local_row * 2 + 1]);
                    D_complexData.emplace_back(segment.data_D[local_row * 2], segment.data_D[local_row * 2 + 1]);
                } else {
                    S_complexData.emplace_back(0.0f, 0.0f);
                    D_complexData.emplace_back(0.0f, 0.0f);
                }
            }
        }

        auto t_fft_end = std::chrono::high_resolution_clock::now();

        // 计算俯仰角、方位角、距离等信息
        spdlog::debug("开始俯仰角计算");
        auto results = computeElevationAngles_GPU(detection_data->S_head, S_complexData, D_complexData, centers, 1024, 2048);
        auto t_compute_end = std::chrono::high_resolution_clock::now();

        if (results.empty()) {
            spdlog::warn("俯仰角计算没有结果，跳过跟踪");
            ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
            *num_tracks = 0;
            return;
        }

        spdlog::info("FFT和俯仰角计算完成，得到 {} 个结果", results.size());

        // 将计算结果转换为Point格式
        std::vector<Point> current_detections;
        current_detections.reserve(results.size());

        for (const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] : results) {
            Point p;
            p.position[0] = x;
            p.position[1] = y;
            p.position[2] = z;
            p.velocity[0] = vx;
            p.velocity[1] = vy;
            p.velocity[2] = vz;
            p.type = 1; // 默认类型
            p.frame = frame;
            p.label = -1; // 初始化标签
            current_detections.push_back(p);
        }

        // 记录FFT和计算性能
        int t_fft = std::chrono::duration_cast<std::chrono::milliseconds>(t_fft_end - t_fft_start).count();
        int t_compute = std::chrono::duration_cast<std::chrono::milliseconds>(t_compute_end - t_fft_end).count();
        spdlog::info("FFT处理耗时: {}ms, 俯仰角计算耗时: {}ms", t_fft, t_compute);

        // 检查是否需要处理分组逻辑
        int current_frame = detection_data->detection_results[0].frame;
        float current_azimuth = detection_data->S_head[0].angle / 100.0f; // 从帧头获取方位角

        // 检查方位角是否变化
        g_azimuth_unchanged = (g_prev_azimuth != -999.0f) &&
                              (std::abs(current_azimuth - g_prev_azimuth) < 1.0f);
        g_prev_azimuth = current_azimuth;

        // 添加到当前组
        for (const auto& detection : current_detections) {
            g_current_group_detections.push_back(detection);
        }

        if (g_group_start_frame == -1) {
            g_group_start_frame = current_frame;
        }

        // 判断是否需要处理当前组
        bool should_process_group = false;
        if (g_azimuth_unchanged) {
            should_process_group = true;
            spdlog::info("云台未转动");
        } else {
            int frame_diff = current_frame - g_group_start_frame;
            if (frame_diff < 0) frame_diff += 65536; // 处理帧号回绕
            if (frame_diff >= FRAMES_PER_GROUP - 1) {
                should_process_group = true;
            }
        }

        std::vector<TrackResult> tracks;

        if (should_process_group && !g_current_group_detections.empty()) {
            spdlog::info("目标数量:{}, 起始帧:{}, 结束帧:{}",
                        g_current_group_detections.size(), g_group_start_frame, current_frame);

            // 聚类检测结果
            auto clustered = clusterDetections_DBSCAN(g_current_group_detections, *g_config_manager->get<float>("/algorithm_settings/cluster/eps"),
                                                    *g_config_manager->get<int>("/algorithm_settings/cluster/min_pts"),
                                                    *g_config_manager->get<int>("/algorithm_settings/cluster/num_threads"));
            // spdlog::info("聚类后数量: {}", clustered.size());

            // 执行跟踪
            tracks = g_tracker->update(clustered);
            // auto tracked_info = convertTracksToTargetInfos(tracks);

            spdlog::info("跟踪结果数量: {}", tracks.size());

            // 轨迹插值
            auto interpolated_tracks = g_tracker->interpolateTracks_seg(1.0f);
            for (const auto& step : interpolated_tracks) {
                // auto step_info = convertTracksToTargetInfos(step);
                tracks.insert(tracks.end(), step.begin(), step.end());
            }

            // 清理当前组（同时释放多余容量，避免长期内存占用攀升）
            g_current_group_detections.clear();
            g_current_group_detections.shrink_to_fit();
            g_group_start_frame = g_azimuth_unchanged ? -1 : current_frame + 1;
        } else {
            *num_tracks = 0;
            ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
            return;
        }

        // 转换跟踪结果
        if (tracks.empty()) {
            *num_tracks = 0;
            ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
            return;
        }

        // 限制输出结果数量
        int actual_tracks = std::min(static_cast<int>(tracks.size()), max_tracks);
        *num_tracks = actual_tracks;

        if (actual_tracks < static_cast<int>(tracks.size())) {
            spdlog::warn("跟踪结果数量 {} 超过最大限制 {}，只输出前 {} 个结果",
                        tracks.size(), max_tracks, actual_tracks);
        }

        // 填充跟踪结果
        for (int i = 0; i < actual_tracks; ++i) {
            const auto& track = tracks[i];

            tracking_results[i].id = track.id;
            tracking_results[i].x = track.position[0];
            tracking_results[i].y = track.position[1];
            tracking_results[i].z = track.position[2];
            tracking_results[i].vx = track.velocity[0];
            tracking_results[i].vy = track.velocity[1];
            tracking_results[i].vz = track.velocity[2];

            // 计算转换值
            float range = std::sqrt(track.position[0]*track.position[0] +
                                    track.position[1]*track.position[1] +
                                    track.position[2]*track.position[2]);
            tracking_results[i].fMR = range;
            tracking_results[i].fMV = (track.position[0]*track.velocity[0] +
                                            track.position[1]*track.velocity[1] +
                                            track.position[2]*track.velocity[2]) / (range + 1e-6f);
            tracking_results[i].fMA = std::fmod(std::atan2(track.position[1], track.position[0]) * 180.0f / M_PI + 360.0f, 360.0f);
            tracking_results[i].fME = std::atan2(track.position[2], std::sqrt(track.position[0]*track.position[0] + track.position[1]*track.position[1])) * 180.0f / M_PI;


            // 设置默认值
            tracking_results[i].fSNR = 1.0f;
            tracking_results[i].fEn = 1.0f;
            tracking_results[i].fRcs = 1.0f;
            tracking_results[i].type = 1;
            tracking_results[i].FPGATimeLog = 1;
            tracking_results[i].PreShow = 2;

            spdlog::info("Track[{:>2}] ID: {:>2} | Pos: ({:>7.2f}, {:>7.2f}, {:>7.2f}) m | Vel: ({:>6.2f}, {:>6.2f}, {:>6.2f}) m/s | R: {:>6.2f} m | Vr: {:>6.2f} m/s | Az: {:>6.2f}° | El: {:>6.2f}°",
                i,
                tracking_results[i].id,
                tracking_results[i].x, tracking_results[i].y, tracking_results[i].z,
                tracking_results[i].vx, tracking_results[i].vy, tracking_results[i].vz,
                tracking_results[i].fMR, tracking_results[i].fMV,
                tracking_results[i].fMA, tracking_results[i].fME);
        }

        // MemoryMonitor::logMemoryUsage("After TargetTracking");
        // MemoryMonitor::logCPUMemoryUsage("After TargetTracking");

        // 记录总体性能信息
        auto t_total_end = std::chrono::high_resolution_clock::now();
        int t_total = std::chrono::duration_cast<std::chrono::milliseconds>(t_total_end - t_fft_start).count();

        spdlog::info("跟踪完成，输出 {} 个轨迹，总耗时: {}ms (FFT: {}ms, 计算: {}ms)",
                     *num_tracks, t_total, t_fft, t_compute);

        if (batch_cb && *num_tracks > 0) {
            constexpr int kBatchSize = 6;
            int num_batches = *num_tracks / kBatchSize;
            for (int i = 0; i < kBatchSize; ++i) {
                int start = i * num_batches;
                int end = start + num_batches;
                batch_cb(tracking_results + start, end - start);
            }
        }
        MemoryMonitor::logCPUMemoryUsage("After TargetTracking");
        ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
        return;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetTracking: {}", e.what());
        ReleaseInternalDetectionData(const_cast<InternalDetectionData*>(detection_data));
        *num_tracks = 0;
        return;
    }
}

// 释放内部检测数据内存
ALGORITHM_API void ReleaseInternalDetectionData(InternalDetectionData* detection_data) {
    if (!detection_data) {
        return;
    }

    // 释放检测结果
    if (detection_data->detection_results) {
        delete[] detection_data->detection_results;
        detection_data->detection_results = nullptr;
        spdlog::debug("Detection results memory released");
    }

    // 释放列数据段
    if (detection_data->column_segments) {
        for (int i = 0; i < detection_data->num_segments; ++i) {
            if (detection_data->column_segments[i].data_S) {
                delete[] detection_data->column_segments[i].data_S;
                detection_data->column_segments[i].data_S = nullptr;
            }
            if (detection_data->column_segments[i].data_D) {
                delete[] detection_data->column_segments[i].data_D;
                detection_data->column_segments[i].data_D = nullptr;
            }
        }
        delete[] detection_data->column_segments;
        detection_data->column_segments = nullptr;
        spdlog::debug("Column segments memory released");
    }

    // 重置计数器
    detection_data->num_detections = 0;
    detection_data->num_segments = 0;
    detection_data->S_head = nullptr;
}

// 释放所有资源
ALGORITHM_API void ReleaseAllResources() {
    try {
        spdlog::info("Resource cleanup started...");

        // 释放算法资源
        CleanupAlgorithmResources();

        // 重置跟踪器和相关状态
        g_tracker.reset();
        g_current_group_detections.clear();
        g_current_group_detections.shrink_to_fit();
        g_group_start_frame = -1;
        g_prev_azimuth = -999.0f;
        g_azimuth_unchanged = false;

        // 重置配置管理器
        g_config_manager.reset();

        g_fft.reset();

        s_lib_initialized.store(false, std::memory_order_release);
        s_init_failed.store(false, std::memory_order_release);

        // 释放全局资源管理器
        releaseGPUResourceManager();
        releaseMemoryPoolManager();

        // 重置CUDA设备以确保所有资源被释放
        cudaError_t cuda_status = cudaDeviceReset();
        if (cuda_status != cudaSuccess) {
            spdlog::warn("CUDA device reset failed: {}", cudaGetErrorString(cuda_status));
        } else {
            spdlog::info("CUDA device reset successfully");
        }

        spdlog::info("All resources released successfully...");

    } catch (const std::exception& e) {
        spdlog::error("Exception during resource cleanup: {}", e.what());
    }
}